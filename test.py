from manim import *
import numpy as np

class MeanStdAnimation(Scene):
    def construct(self):
        # 设置标题（使用英文）
        title = Text("Mean and Standard Deviation vs Number of 1s", font_size=32)
        title.to_edge(UP)
        self.play(Write(title))
        
        # 创建坐标轴
        axes = Axes(
            x_range=[1, 11, 1],
            y_range=[0, 0.6, 0.1],
            x_length=10,
            y_length=6,
            axis_config={"color": BLUE},
            x_axis_config={
                "numbers_to_include": np.arange(2, 11, 1),
            },
            y_axis_config={
                "numbers_to_include": np.arange(0, 0.7, 0.1),
            },
            tips=False,
        )
        
        # 坐标轴标签（使用英文）
        x_label = Text("Number of 1s", font_size=20).next_to(axes.x_axis, DOWN)
        y_label = Text("Value", font_size=20).next_to(axes.y_axis, LEFT).rotate(PI/2)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        
        # 创建图例
        legend_mean = VGroup(
            Line(ORIGIN, RIGHT * 0.5, color=GREEN, stroke_width=4),
            Text("Mean", font_size=20, color=GREEN)
        ).arrange(RIGHT, buff=0.1)
        
        legend_std = VGroup(
            Line(ORIGIN, RIGHT * 0.5, color=RED, stroke_width=4),
            Text("Std Dev", font_size=20, color=RED)
        ).arrange(RIGHT, buff=0.1)
        
        legend = VGroup(legend_mean, legend_std).arrange(DOWN, buff=0.2)
        legend.to_corner(UR, buff=0.5)
        self.play(Write(legend))
        
        # 存储数据点
        mean_points = []
        std_points = []
        mean_values = []
        std_values = []
        
        # 创建序列显示区域
        sequence_title = Text("Current sequence:", font_size=20)
        sequence_title.to_edge(DOWN, buff=2.5)
        self.play(Write(sequence_title))
        
        # 动画演示i从2到10的过程
        for i in range(2, 11):
            # 创建序列（前i个位置为1，其余为0）
            sequence = [1] * i + [0] * (20 - i)
            
            # 计算均值和标准差
            mean_val = np.mean(sequence)
            std_val = np.std(sequence, ddof=0)  # 使用总体标准差
            
            mean_values.append(mean_val)
            std_values.append(std_val)
            
            # 显示当前序列（只显示前10个元素）
            sequence_str = str(sequence[:10]).replace(',', ' ')
            sequence_display = Text(
                f"{sequence_str} ...", 
                font_size=16
            )
            sequence_display.next_to(sequence_title, DOWN)
            
            # 显示统计信息
            stats_text = Text(
                f"Count: {i}  Mean: {mean_val:.3f}  Std: {std_val:.3f}",
                font_size=16
            )
            stats_text.next_to(sequence_display, DOWN)
            
            if i == 2:
                self.play(Write(sequence_display), Write(stats_text))
            else:
                # 更新显示
                new_sequence_display = Text(f"{sequence_str} ...", font_size=16)
                new_sequence_display.next_to(sequence_title, DOWN)
                new_stats_text = Text(
                    f"Count: {i}  Mean: {mean_val:.3f}  Std: {std_val:.3f}",
                    font_size=16
                )
                new_stats_text.next_to(new_sequence_display, DOWN)
                
                self.play(
                    Transform(sequence_display, new_sequence_display),
                    Transform(stats_text, new_stats_text)
                )
            
            # 在图上添加点
            mean_point = axes.coords_to_point(i, mean_val)
            std_point = axes.coords_to_point(i, std_val)
            
            mean_dot = Dot(mean_point, color=GREEN, radius=0.06)
            std_dot = Dot(std_point, color=RED, radius=0.06)
            
            self.play(Create(mean_dot), Create(std_dot))
            
            mean_points.append(mean_dot)
            std_points.append(std_dot)
            
            # 如果不是第一个点，画连线
            if i > 2:
                mean_line = Line(
                    axes.coords_to_point(i-1, mean_values[-2]),
                    axes.coords_to_point(i, mean_values[-1]),
                    color=GREEN,
                    stroke_width=3
                )
                std_line = Line(
                    axes.coords_to_point(i-1, std_values[-2]),
                    axes.coords_to_point(i, std_values[-1]),
                    color=RED,
                    stroke_width=3
                )
                self.play(Create(mean_line), Create(std_line))
            
            self.wait(0.5)
        
        # 计算并显示斜率
        self.wait(1)
        
        # 使用线性回归计算斜率
        x_data = np.array(range(2, 11))
        mean_slope = np.polyfit(x_data, mean_values, 1)[0]
        std_slope = np.polyfit(x_data, std_values, 1)[0]
        
        # 显示斜率比较
        slope_text = VGroup(
            Text(f"Mean slope: {mean_slope:.4f}", font_size=20, color=GREEN),
            Text(f"Std slope: {std_slope:.4f}", font_size=20, color=RED),
            Text(f"Std slope > Mean slope: {std_slope > mean_slope}", 
                 font_size=20, color=YELLOW)
        ).arrange(DOWN, buff=0.2)
        
        slope_text.to_corner(UL, buff=0.5)
        self.play(Write(slope_text))
        
        # 添加结论
        conclusion = Text(
            "Conclusion: Standard deviation grows faster than mean!",
            font_size=24,
            color=YELLOW
        )
        conclusion.to_edge(DOWN, buff=0.5)
        self.play(Write(conclusion))
        
        self.wait(3)

# 运行命令：manim -pql test.py MeanStdAnimation