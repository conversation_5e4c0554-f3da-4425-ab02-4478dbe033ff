# QuantStats-Reloaded：拯救被"花花世界"迷了眼的神器

## 🎬 视频脚本大纲

### 【开场】引入话题（30秒）

**画面：** QuantStats logo + 代码界面

**文案：**
"大家好！今天要给大家介绍一个量化投资必备神器——QuantStats！

这是一款专门用于交易策略绩效分析的Python库，在GitHub上拥有超过5.8k的stars，深受量化圈用户喜爱。

但是！最近很多小伙伴发现，这个神器竟然用不了了！特别是Python 3.12的用户，简直是欲哭无泪...

今天我们就来聊聊这背后的故事，以及我们的解决方案！"

---

### 【第一部分】QuantStats简介（1分钟）

**画面：** QuantStats功能展示 + 作者介绍

**文案：**
"首先简单介绍一下QuantStats。

QuantStats是由Ran Aroussi创建的开源项目。这位大神可不简单：
- 软件开发者 ✨
- 金融创新者 💰  
- 独立创业者 🚀
- 播客主持人 🎙️
![alt text](image.png)
他创建了多个超受欢迎的Python库，比如大名鼎鼎的YFinance（17.9k stars）！还创立了Tradologics程序化交易云平台，目前还主持着『Old School, New Tech』播客节目。

QuantStats的主要作用是什么呢？

简单说，就是帮你评价投资策略的好坏！

假设你已经制定了一个投资策略，并且通过回测工具计算出了每日的历史收益。接下来你肯定想知道：这个策略到底怎么样？风险如何？收益如何？

QuantStats就是干这个的！它能帮你计算各种专业指标，比如夏普比率、信息比率、最大回撤、α和β收益等等。"
![alt text](image-1.png)
---

### 【第二部分】作者"失踪"之谜（1.5分钟）

**画面：** 对比图：播客更新 vs QuantStats更新时间

**文案：**
"但是！问题来了...

咱们的主角Ran Aroussi简直是个'斜杠青年'的典型代表：开源大神、金融创新者、独立创业者、播客主持人...身兼数职，忙得不亦乐乎！

他就像个'代码界的花花公子'，到处留情：
- YFinance拿了17.9k个star ⭐
- QuantStats也是圈粉无数 💕  
- 还开了个Tradologics云平台当老板 👔
- 闲暇时间还要录播客聊人生哲学 🎧

结果呢？**贪多嚼不烂**！

【重点对比】
- 播客最新更新：2025年5月15日 ✅
  ![alt text](image-2.png)
- QuantStats最后更新：8个月前 ❌

这差距也太明显了吧！

QuantStats这个'亲儿子'已经8个月没人管了😭，GitHub上的issue堆得像小山一样🏔️，Python 3.12用户哭着喊着'爸爸快回来修bug'👶...

**花花世界迷人眼，Ran你要赶快脱身，出来造福人类啊！！！**"

---

### 【第三部分】问题有多严重（1分钟）

**画面：** 错误截图 + GitHub issue页面


**文案：**
"问题到底有多严重呢？

在Python 3.12版本下，你会遇到这样的错误：
![alt text](image-3.png)

这是一个只在Jupyter Notebook下出现的问题，主要由于nbformat升级导致。

修复这个问题后，你还可能遇到：
![alt text](image-4.png)

几个月来，尽管社区不断提交修复方案，但作者一直没有时间发布新版本。

可能因为原版QuantStats没有单元测试和CI，每次发布都需要手动测试，极为繁琐，作者实在是抽不出身来。

这就导致了一个尴尬的局面：工具很好用，但是用不了！"

---

### 【第四部分】我们的解决方案（2分钟）

**画面：** quantstats-reloaded介绍 + 修复内容展示

**文案：**
"为了'普渡苍生'，满足大家的应用需求，我们决定出手了！

我们接手了QuantStats的维护工作，并发布了**quantstats-reloaded**新包！
![alt text](image-5.png)
（因为原包只能由Ran Aroussi发布，所以我们只能另起炉灶）

**我们主要修复了什么？**

1. **修复了#416、#420等关键bug**
   - 解决了Python 3.12兼容性问题
   - 修复了Jupyter Notebook运行错误
   - 解决了依赖包版本冲突

2. **进行了重要重构**
   - 添加了完整的单元测试
   - 建立了CI/CD流程
   - 优化了代码结构
   - 提升了稳定性

3. **增强了功能**
   - 保持了原有的所有功能
   - 提升了运行效率
   - 增强了错误处理

**安装使用超简单：**
```bash
pip install quantstats-reloaded
```

然后就可以正常使用了！API完全兼容原版QuantStats。"

---
!!!info 我们已经制定了一个投资策略，并且已经利用回测工具，计算出了策略的每日的历史回测收益。接下来我们要做的肯定是，想办法评价一下他，这个策略是好是坏，风险如何，收益如何 ➡️ Quantstats

第一部分：数据获取与处理
数据获取：使用Tushare的pro.index_daily函数获取沪深300指数(代码为"000300.SH")的日线数据，时间范围大家可以根据需求自行设置
设置日期索引：将"trade_date"列转换为datetime格式并设为索引，因为tushare返回的日期格式是字符串，不能直接用于日期计算，所以需要转换为datetime格式，这样便于后续进行基于时间的分析和绘图。
排序处理：按日期升序排列数据，确保数据是从早到晚的时间顺序。

第二步：定义函数
使用rolling函数创建18天的滑动窗口(默认值)，计算窗口内低价和高价的协方差，计算窗口内低价的方差，两者相除得到RSRS因子。前win-1天的值将是NaN，因为不足18天。

第三步：
