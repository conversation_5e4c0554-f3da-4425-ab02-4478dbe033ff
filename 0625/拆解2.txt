今天拆解温逸飞老师的视频
技术上其实没有什么很难的点
难的是整个画面的设计和想法
那今天这期主要分为三个重点的片段
第一个片段
上方的云朵滚动效果
楼房的生长动画以及字幕动画组成
第二个片段
分屏出现加文字动画
第三个片段
这里有一个嵌入老电视机的
一个设计人物的出场
加字幕动画
首先我们剪第一个片段
素材我已经都找好了
我们这里是黑底的云朵
素材不是透明格式
需要先将混合模式调整为绿色
这样的黑底就消失了
第二步原视频
这里的生长动画是扣了三层的效果
那我们尽量还原
我们先扣矮的房子
我就简略的扣一下
然后第二层中层的房子
最后高层的房子
抠完后把饱和度拉低
在做生长动画前要先调整图层关系
矮的房子是在上层的
动画部分
我们可以用入场动画
或者打关键帧来实现
这里我打的是关键帧
再加上一个缓动的预设会更丝滑
做完后我们接一个航拍的素材
加上转场里面的散白2
这样
我们第一个片段的画面部分就完成了
接着做文字部分
这里需要用到冰雪飘动和翻动
这两个入场动画
台词切换时给文字剪一刀
更换文本内容
添加出场动画和入场动画翻动
因为我们后面是需要整体打关键帧的
我们先给两层文本进行复合
片段这开头给他一个侧滑的入场动画
并在合适的位置
打上大小和旋转的关键帧
略微进行放大和旋
转另外一层
打上位置关键帧
让它从右往左缓慢移动
这个地方
我们需要做一个字幕分割的效果
给字幕新建复合片段
添加线性蒙版
复制一层
点击蒙版翻转
这样就得到了一个看似完整
实则已经分成两半的字母
给他们分别打上关键帧
第一个片段就做完了
接着第二个片段
添加人群素材后转场
选择胶片擦除
这里的分屏效果也是利用到蒙版
中间的素材选择镜面蒙版
两边素材可以选择线性蒙版
调整位置即可
这边我记得有一个这样的渐变花字
但是我没有找到
但是没关系
给文本进行复合编段
添加线性蒙版
调整羽化值即可
选择入场动画站起
最后一个片段
首先把刚才做的第二个片段的内容
全部附耳边段
在贴纸里搜索电视机
找到这个贴纸
给给他缩放到画面外
打上关键帧
在电视机内的素材同步缩小即可
电视机内有一个素材的变换
添加素材缩放到合适的大小
再添加一个故障转场的效果即可
再添加人物素材
进行一个抠像处理
添加向上滑动的入场动画就可以了
后面基本上都是一些文字动画
这里我就不多讲了
相信大家都会
那我们看看最后效果
云南的经济
马上就会迎来一个全新的大机遇
云南接下来还有没有发展
以前云南的年轻人不不是进烟厂
就是去干导游
或者就是去广东打工
很少有别的出路
但是
云南的前景可能会有一个很大的转折
具体是什么这
对普通人又有什么影响
我是飞飞
一个喜欢保护韭菜的财经博主
