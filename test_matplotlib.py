import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
from matplotlib.patches import Rectangle

def create_mean_std_animation():
    """创建均值和标准差变化的动画演示"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 计算所有数据
    i_values = list(range(2, 11))
    mean_values = []
    std_values = []
    sequences = []
    
    print("计算过程:")
    print("=" * 60)
    
    for i in i_values:
        sequence = [1] * i + [0] * (20 - i)
        mean_val = np.mean(sequence)
        std_val = np.std(sequence, ddof=0)  # 使用总体标准差
        
        sequences.append(sequence)
        mean_values.append(mean_val)
        std_values.append(std_val)
        
        print(f"1的个数: {i:2d} | 均值: {mean_val:.4f} | 标准差: {std_val:.4f}")
    
    # 计算斜率
    mean_slope = np.polyfit(i_values, mean_values, 1)[0]
    std_slope = np.polyfit(i_values, std_values, 1)[0]
    
    print("\n" + "=" * 60)
    print(f"均值斜率: {mean_slope:.6f}")
    print(f"标准差斜率: {std_slope:.6f}")
    print(f"标准差斜率 > 均值斜率: {std_slope > mean_slope}")
    print("=" * 60)
    
    # 创建图形
    fig = plt.figure(figsize=(15, 10))
    
    # 主图：均值和标准差变化
    ax1 = plt.subplot(2, 2, (1, 2))
    
    # 绘制数据点和连线
    line_mean, = ax1.plot(i_values, mean_values, 'go-', linewidth=3, markersize=8, 
                         label=f'均值 (斜率: {mean_slope:.4f})')
    line_std, = ax1.plot(i_values, std_values, 'ro-', linewidth=3, markersize=8, 
                        label=f'标准差 (斜率: {std_slope:.4f})')
    
    # 绘制拟合直线
    x_fit = np.linspace(2, 10, 100)
    mean_fit = mean_slope * (x_fit - 2) + mean_values[0]
    std_fit = std_slope * (x_fit - 2) + std_values[0]
    
    ax1.plot(x_fit, mean_fit, 'g--', alpha=0.7, linewidth=2, label='均值拟合线')
    ax1.plot(x_fit, std_fit, 'r--', alpha=0.7, linewidth=2, label='标准差拟合线')
    
    ax1.set_xlabel('1的个数', fontsize=14)
    ax1.set_ylabel('数值', fontsize=14)
    ax1.set_title('均值和标准差随1的个数变化', fontsize=16, fontweight='bold')
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(1.5, 10.5)
    ax1.set_ylim(0, 0.6)
    
    # 添加斜率比较文本
    comparison_text = f"标准差斜率 ({std_slope:.4f}) > 均值斜率 ({mean_slope:.4f}): {std_slope > mean_slope}"
    ax1.text(0.02, 0.98, comparison_text, transform=ax1.transAxes, 
             fontsize=12, verticalalignment='top', 
             bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.8))
    
    # 序列可视化
    ax2 = plt.subplot(2, 2, 3)
    ax3 = plt.subplot(2, 2, 4)
    
    # 显示几个关键序列状态
    for idx, (ax, i_idx) in enumerate(zip([ax2, ax3], [0, 8])):
        i = i_values[i_idx]
        sequence = sequences[i_idx]
        
        # 创建条形图
        colors = ['red' if x == 1 else 'lightblue' for x in sequence]
        bars = ax.bar(range(20), sequence, color=colors, edgecolor='black', linewidth=0.5)
        
        ax.set_title(f'序列状态 (1的个数: {i})\n均值: {mean_values[i_idx]:.3f}, 标准差: {std_values[i_idx]:.3f}', 
                    fontsize=12)
        ax.set_xlabel('位置', fontsize=10)
        ax.set_ylabel('值', fontsize=10)
        ax.set_ylim(0, 1.2)
        ax.set_xlim(-0.5, 19.5)
        
        # 添加数值标签
        for j, (bar, val) in enumerate(zip(bars, sequence)):
            if val == 1:
                ax.text(j, val + 0.05, '1', ha='center', va='bottom', fontsize=8)
    
    plt.tight_layout()
    
    # 添加总标题
    fig.suptitle('长度为20的序列中1的个数对均值和标准差的影响', 
                fontsize=18, fontweight='bold', y=0.98)
    
    plt.show()
    
    return mean_slope, std_slope, mean_values, std_values

def create_animated_demo():
    """创建逐步演示动画"""
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 数据存储
    i_values = []
    mean_values = []
    std_values = []
    
    # 初始化图形元素
    line_mean, = ax1.plot([], [], 'g-o', linewidth=3, markersize=8, label='均值')
    line_std, = ax1.plot([], [], 'r-o', linewidth=3, markersize=8, label='标准差')
    
    # 设置坐标轴
    ax1.set_xlim(1, 11)
    ax1.set_ylim(0, 0.6)
    ax1.set_xlabel('1的个数', fontsize=12)
    ax1.set_ylabel('数值', fontsize=12)
    ax1.set_title('均值和标准差随1的个数变化', fontsize=14, fontweight='bold')
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # 序列显示
    ax2.set_xlim(0, 20)
    ax2.set_ylim(-0.5, 1.5)
    ax2.set_xlabel('位置', fontsize=12)
    ax2.set_ylabel('值', fontsize=12)
    ax2.set_title('当前序列状态', fontsize=14, fontweight='bold')
    
    # 文本显示
    text_info = ax1.text(0.02, 0.98, '', transform=ax1.transAxes, 
                        verticalalignment='top', fontsize=11,
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    def animate(frame):
        i = frame + 2  # 从2开始
        if i > 10:
            return line_mean, line_std, text_info
        
        # 创建序列
        sequence = [1] * i + [0] * (20 - i)
        mean_val = np.mean(sequence)
        std_val = np.std(sequence, ddof=0)
        
        # 更新数据
        i_values.append(i)
        mean_values.append(mean_val)
        std_values.append(std_val)
        
        # 更新线条
        line_mean.set_data(i_values, mean_values)
        line_std.set_data(i_values, std_values)
        
        # 更新序列显示
        ax2.clear()
        colors = ['red' if x == 1 else 'lightblue' for x in sequence]
        ax2.bar(range(20), sequence, color=colors, edgecolor='black', linewidth=0.5)
        ax2.set_xlim(-0.5, 19.5)
        ax2.set_ylim(-0.1, 1.1)
        ax2.set_xlabel('位置', fontsize=12)
        ax2.set_ylabel('值', fontsize=12)
        ax2.set_title(f'当前序列状态 (1的个数: {i})', fontsize=14, fontweight='bold')
        
        # 更新信息文本
        info_text = f'1的个数: {i}\n均值: {mean_val:.4f}\n标准差: {std_val:.4f}'
        if len(mean_values) > 1:
            mean_slope = (mean_values[-1] - mean_values[0]) / (i_values[-1] - i_values[0])
            std_slope = (std_values[-1] - std_values[0]) / (i_values[-1] - i_values[0])
            info_text += f'\n均值斜率: {mean_slope:.4f}\n标准差斜率: {std_slope:.4f}'
            info_text += f'\n标准差斜率 > 均值斜率: {std_slope > mean_slope}'
        
        text_info.set_text(info_text)
        
        return line_mean, line_std, text_info
    
    # 创建动画
    anim = animation.FuncAnimation(fig, animate, frames=9, interval=1500, 
                                 blit=False, repeat=True)
    
    plt.tight_layout()
    return fig, anim

def create_detailed_analysis():
    """创建详细的数学分析"""

    print("\n" + "🔍 详细数学分析")
    print("=" * 80)

    # 理论分析
    print("📐 理论公式:")
    print("对于长度为20的序列，前i个位置为1:")
    print("• 均值 = i/20")
    print("• 标准差 = √[i(20-i)/400] = √[i(20-i)]/20")
    print()

    # 计算理论斜率
    i_range = np.array(range(2, 11))
    theoretical_means = i_range / 20
    theoretical_stds = np.sqrt(i_range * (20 - i_range)) / 20

    theoretical_mean_slope = np.polyfit(i_range, theoretical_means, 1)[0]
    theoretical_std_slope = np.polyfit(i_range, theoretical_stds, 1)[0]

    print("📊 理论斜率:")
    print(f"• 均值理论斜率: {theoretical_mean_slope:.6f} (= 1/20 = 0.05)")
    print(f"• 标准差理论斜率: {theoretical_std_slope:.6f}")
    print(f"• 标准差斜率 > 均值斜率: {theoretical_std_slope > theoretical_mean_slope}")
    print()

    # 数值验证
    print("🧮 数值验证:")
    i_values = list(range(2, 11))
    actual_means = [i/20 for i in i_values]
    actual_stds = [np.sqrt(i*(20-i))/20 for i in i_values]

    actual_mean_slope = np.polyfit(i_values, actual_means, 1)[0]
    actual_std_slope = np.polyfit(i_values, actual_stds, 1)[0]

    print(f"• 实际均值斜率: {actual_mean_slope:.6f}")
    print(f"• 实际标准差斜率: {actual_std_slope:.6f}")
    print(f"• 验证结果: 标准差增长速度确实比均值更快！")
    print()

    # 解释原因
    print("💡 原因解释:")
    print("• 均值是线性函数: mean = i/20，斜率恒定为 1/20 = 0.05")
    print("• 标准差是非线性函数: std = √[i(20-i)]/20")
    print("• 在i=2到10的范围内，标准差函数的平均增长率大于均值的增长率")
    print("• 这是因为√[i(20-i)]在该区间内增长较快")

def create_comparison_table():
    """创建详细的数据对比表"""

    print("\n" + "📊 详细数据对比表")
    print("=" * 100)
    print(f"{'1的个数':>8} {'序列示例':>25} {'均值':>10} {'标准差':>10} {'均值增量':>10} {'标准差增量':>12}")
    print("=" * 100)

    i_values = list(range(2, 11))
    prev_mean = None
    prev_std = None

    for i in i_values:
        sequence = [1] * i + [0] * (20 - i)
        mean_val = np.mean(sequence)
        std_val = np.std(sequence, ddof=0)

        # 序列示例（显示前8个元素）
        seq_str = str(sequence[:8]).replace(',', '').replace('[', '').replace(']', '') + '...'

        # 计算增量
        mean_delta = mean_val - prev_mean if prev_mean is not None else 0
        std_delta = std_val - prev_std if prev_std is not None else 0

        print(f"{i:>8d} {seq_str:>25} {mean_val:>10.4f} {std_val:>10.4f} {mean_delta:>10.4f} {std_delta:>12.4f}")

        prev_mean = mean_val
        prev_std = std_val

    print("=" * 100)

if __name__ == "__main__":
    print("🎯 均值和标准差变化演示")
    print("=" * 80)

    # 选择演示模式
    print("请选择演示模式:")
    print("1. 静态图表分析")
    print("2. 动态演示动画")
    print("3. 详细数学分析")
    print("4. 数据对比表")
    print("5. 全部演示")

    choice = input("请输入选择 (1-5): ").strip()

    if choice == "1" or choice == "5":
        print("\n📈 运行静态图表分析...")
        mean_slope, std_slope, mean_vals, std_vals = create_mean_std_animation()

    if choice == "2" or choice == "5":
        print("\n🎬 运行动态演示动画...")
        fig, anim = create_animated_demo()
        plt.show()

        # 如果要保存为gif
        save_gif = input("是否保存为GIF动画? (y/n): ").strip().lower()
        if save_gif == 'y':
            print("正在保存动画...")
            anim.save('mean_std_animation.gif', writer='pillow', fps=0.8)
            print("动画已保存为 mean_std_animation.gif")

    if choice == "3" or choice == "5":
        create_detailed_analysis()

    if choice == "4" or choice == "5":
        create_comparison_table()

    if choice == "5":
        # 最终结论
        print("\n" + "🎯 最终结论")
        print("=" * 80)
        print(f"在长度为20的序列中，当1的个数从2增加到10时：")

        # 重新计算斜率用于最终结论
        i_values = list(range(2, 11))
        mean_values = [i/20 for i in i_values]
        std_values = [np.sqrt(i*(20-i))/20 for i in i_values]

        mean_slope = np.polyfit(i_values, mean_values, 1)[0]
        std_slope = np.polyfit(i_values, std_values, 1)[0]

        print(f"• 均值斜率: {mean_slope:.6f}")
        print(f"• 标准差斜率: {std_slope:.6f}")
        print(f"• 结论: 标准差的增长速度确实比均值更快！")
        print("=" * 80)
