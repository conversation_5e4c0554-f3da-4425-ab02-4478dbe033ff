# 问题1：N次抛硬币不出现连续两个正面的概率

## 题目
What's the probability of not seeing HH in N coin tosses?

## 解答

### 方法1：递推关系

设 $P(n)$ 为 n 次抛硬币不出现连续两个正面的概率。

**递推思路：**
- 如果第 n 次抛出反面(T)，前 n-1 次的任何合法序列都可以
- 如果第 n 次抛出正面(H)，第 n-1 次必须是反面(T)，前 n-2 次可以是任何合法序列

**递推公式：**
```
P(n) = P(n-1) × 1/2 + P(n-2) × 1/4
```

解释：
- P(n-1) × 1/2：前n-1次合法，第n次抛T
- P(n-2) × 1/4：前n-2次合法，第n-1次抛T，第n次抛H

**边界条件：**
- P(1) = 1 (只有一次抛硬币，不可能出现HH)
- P(2) = 3/4 (TT, TH, HT都合法，只有HH不合法)

### 方法2：状态转移

定义状态：
- 状态0：当前序列以T结尾
- 状态1：当前序列以H结尾
- 状态2：出现了HH（吸收状态）

**转移矩阵：**
```
     T    H   HH
T  [1/2  1/2   0 ]
H  [1/2   0   1/2]
HH [ 0    0    1 ]
```

**递推公式：**
设 $a_n$ = 以T结尾的概率，$b_n$ = 以H结尾的概率

```
a_{n+1} = (a_n + b_n) × 1/2
b_{n+1} = a_n × 1/2
```

初始条件：$a_1 = 1/2$, $b_1 = 1/2$

所求概率：$P(n) = a_n + b_n$

### 方法3：特征方程求解

从递推关系 $P(n) = \frac{1}{2}P(n-1) + \frac{1}{4}P(n-2)$ 出发。

**特征方程：**
```
x² = (1/2)x + 1/4
2x² - x - 1 = 0
```

**特征根：**
```
x₁ = 1, x₂ = -1/2
```

**通解：**
```
P(n) = A × 1ⁿ + B × (-1/2)ⁿ = A + B × (-1/2)ⁿ
```

**利用边界条件求常数：**
- P(1) = 1: A + B × (-1/2) = 1
- P(2) = 3/4: A + B × (1/4) = 3/4

解得：A = 2/3, B = 2/3

**最终公式：**
```
P(n) = (2/3) + (2/3) × (-1/2)ⁿ = (2/3)[1 + (-1/2)ⁿ]
```

### Python验证

```python
def prob_no_consecutive_heads(n):
    """计算n次抛硬币不出现HH的概率"""
    
    # 方法1：递推
    if n == 1:
        return 1.0
    if n == 2:
        return 0.75
    
    p = [0] * (n + 1)
    p[1] = 1.0
    p[2] = 0.75
    
    for i in range(3, n + 1):
        p[i] = 0.5 * p[i-1] + 0.25 * p[i-2]
    
    return p[n]

def prob_formula(n):
    """使用公式计算"""
    return (2/3) * (1 + (-0.5)**n)

# 验证
for n in range(1, 11):
    p1 = prob_no_consecutive_heads(n)
    p2 = prob_formula(n)
    print(f"n={n}: 递推={p1:.6f}, 公式={p2:.6f}")
```

### 蒙特卡洛模拟

```python
import random

def simulate_no_hh(n, trials=100000):
    """蒙特卡洛模拟"""
    success = 0
    
    for _ in range(trials):
        sequence = [random.choice([0, 1]) for _ in range(n)]  # 0=T, 1=H
        
        # 检查是否有连续的HH
        has_hh = False
        for i in range(n-1):
            if sequence[i] == 1 and sequence[i+1] == 1:
                has_hh = True
                break
        
        if not has_hh:
            success += 1
    
    return success / trials

# 验证模拟结果
for n in [5, 10, 15, 20]:
    theoretical = prob_formula(n)
    simulated = simulate_no_hh(n)
    print(f"n={n}: 理论值={theoretical:.4f}, 模拟值={simulated:.4f}")
```

## 答案

**最终公式：**
```
P(n) = (2/3)[1 + (-1/2)ⁿ]
```

**渐近行为：**
当 n → ∞ 时，P(n) → 2/3

**直觉解释：**
随着抛硬币次数增加，不出现HH的概率趋向于2/3，这意味着大约有1/3的概率会出现连续两个正面。
