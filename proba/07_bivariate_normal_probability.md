# 问题7：二元正态分布的条件概率

## 题目
X,Y are iid standard normal. what P(Y>3X)? Given that X>0 what's P(Y>3X|X>0)?

## 解答

### 问题分析

**给定条件：**
- X, Y 独立同分布，都服从标准正态分布 N(0,1)
- 求：P(Y > 3X) 和 P(Y > 3X | X > 0)

### 第一部分：P(Y > 3X)

**方法1：几何方法**

在二维平面上，条件 Y > 3X 表示点(X,Y)位于直线 Y = 3X 的上方。

由于 X, Y 独立且都是标准正态分布，联合密度函数为：
```
f(x,y) = (1/2π) exp(-(x² + y²)/2)
```

这是一个关于原点的圆对称分布。

**关键洞察：**
直线 Y = 3X 通过原点，将平面分成两部分。由于分布关于原点对称，我们需要计算直线上方区域的概率。

**角度计算：**
直线 Y = 3X 的倾斜角为：θ = arctan(3)

上方区域对应的角度范围：[arctan(3), π + arctan(3)]
角度跨度：π

因此：P(Y > 3X) = (π - arctan(3)) / π

**方法2：变量变换**

设 Z = Y - 3X，则：
- Z ~ N(0, 1 + 9) = N(0, 10) （因为X,Y独立）
- P(Y > 3X) = P(Z > 0) = 1/2

等等，这里有问题！让我重新计算。

**正确的变量变换：**
```
Z = Y - 3X
E[Z] = E[Y] - 3E[X] = 0 - 3×0 = 0
Var(Z) = Var(Y) + 9Var(X) = 1 + 9×1 = 10
```

所以 Z ~ N(0, 10)，因此：
P(Y > 3X) = P(Z > 0) = 1/2

### 第二部分：P(Y > 3X | X > 0)

**条件概率计算：**

```
P(Y > 3X | X > 0) = P(Y > 3X, X > 0) / P(X > 0)
```

其中：P(X > 0) = 1/2

**计算分子：**
P(Y > 3X, X > 0) 表示第一象限中直线 Y = 3X 上方的区域。

**方法1：积分计算**
```
P(Y > 3X, X > 0) = ∫₀^∞ ∫₃ₓ^∞ (1/2π) exp(-(x² + y²)/2) dy dx
```

**方法2：几何方法**
在第一象限中，直线 Y = 3X 将区域分成两部分：
- 上方：Y > 3X
- 下方：0 < Y < 3X

由于对称性和角度关系：
- 直线 Y = 3X 的角度：arctan(3)
- 第一象限的角度：π/2
- 上方区域角度：π/2 - arctan(3)

因此：
```
P(Y > 3X, X > 0) = (π/2 - arctan(3)) / (2π) = (π/2 - arctan(3)) / (2π)
```

**最终结果：**
```
P(Y > 3X | X > 0) = P(Y > 3X, X > 0) / P(X > 0)
                   = [(π/2 - arctan(3)) / (2π)] / (1/2)
                   = (π/2 - arctan(3)) / π
```

### Python验证

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import math

def monte_carlo_simulation(n_samples=1000000):
    """蒙特卡洛模拟验证"""
    
    # 生成独立标准正态随机变量
    X = np.random.standard_normal(n_samples)
    Y = np.random.standard_normal(n_samples)
    
    # 计算 P(Y > 3X)
    condition1 = Y > 3 * X
    prob1_sim = np.mean(condition1)
    
    # 计算 P(Y > 3X | X > 0)
    condition2 = (Y > 3 * X) & (X > 0)
    condition3 = X > 0
    prob2_sim = np.sum(condition2) / np.sum(condition3)
    
    # 理论值
    prob1_theory = 0.5  # 从变量变换得到
    prob2_theory = (np.pi/2 - np.arctan(3)) / np.pi
    
    print("蒙特卡洛模拟结果")
    print("=" * 40)
    print(f"样本数量: {n_samples:,}")
    print(f"\nP(Y > 3X):")
    print(f"  模拟值: {prob1_sim:.6f}")
    print(f"  理论值: {prob1_theory:.6f}")
    print(f"  误差: {abs(prob1_sim - prob1_theory):.6f}")
    
    print(f"\nP(Y > 3X | X > 0):")
    print(f"  模拟值: {prob2_sim:.6f}")
    print(f"  理论值: {prob2_theory:.6f}")
    print(f"  误差: {abs(prob2_sim - prob2_theory):.6f}")
    
    return X, Y, prob1_sim, prob2_sim

def analytical_calculation():
    """解析计算"""
    
    print("\n解析计算")
    print("=" * 20)
    
    # P(Y > 3X) 使用变量变换
    print("P(Y > 3X) 计算:")
    print("设 Z = Y - 3X")
    print("E[Z] = E[Y] - 3E[X] = 0")
    print("Var(Z) = Var(Y) + 9Var(X) = 1 + 9 = 10")
    print("Z ~ N(0, 10)")
    print("P(Y > 3X) = P(Z > 0) = 0.5")
    
    # P(Y > 3X | X > 0) 使用几何方法
    print(f"\nP(Y > 3X | X > 0) 计算:")
    print(f"arctan(3) = {np.arctan(3):.6f} 弧度")
    print(f"π/2 = {np.pi/2:.6f} 弧度")
    print(f"π/2 - arctan(3) = {np.pi/2 - np.arctan(3):.6f} 弧度")
    
    prob2 = (np.pi/2 - np.arctan(3)) / np.pi
    print(f"P(Y > 3X | X > 0) = (π/2 - arctan(3))/π = {prob2:.6f}")
    
    return prob2

def visualize_problem(X, Y):
    """可视化问题"""
    
    # 只取前10000个点用于可视化
    n_plot = 10000
    X_plot = X[:n_plot]
    Y_plot = Y[:n_plot]
    
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 图1：完整的散点图
    axes[0].scatter(X_plot, Y_plot, alpha=0.3, s=1)
    x_line = np.linspace(-4, 4, 100)
    y_line = 3 * x_line
    axes[0].plot(x_line, y_line, 'r-', linewidth=2, label='Y = 3X')
    
    # 标记区域
    condition = Y_plot > 3 * X_plot
    axes[0].scatter(X_plot[condition], Y_plot[condition], c='red', alpha=0.5, s=1, label='Y > 3X')
    
    axes[0].set_xlim(-4, 4)
    axes[0].set_ylim(-4, 4)
    axes[0].set_xlabel('X')
    axes[0].set_ylabel('Y')
    axes[0].set_title('P(Y > 3X)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    axes[0].set_aspect('equal')
    
    # 图2：只看第一象限
    mask_positive = X_plot > 0
    X_pos = X_plot[mask_positive]
    Y_pos = Y_plot[mask_positive]
    
    axes[1].scatter(X_pos, Y_pos, alpha=0.3, s=1)
    x_line_pos = np.linspace(0, 4, 100)
    y_line_pos = 3 * x_line_pos
    axes[1].plot(x_line_pos, y_line_pos, 'r-', linewidth=2, label='Y = 3X')
    
    # 标记条件区域
    condition_pos = Y_pos > 3 * X_pos
    axes[1].scatter(X_pos[condition_pos], Y_pos[condition_pos], c='red', alpha=0.5, s=1, label='Y > 3X, X > 0')
    
    axes[1].set_xlim(0, 4)
    axes[1].set_ylim(0, 4)
    axes[1].set_xlabel('X')
    axes[1].set_ylabel('Y')
    axes[1].set_title('P(Y > 3X | X > 0)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    axes[1].set_aspect('equal')
    
    # 图3：角度示意图
    theta = np.linspace(0, 2*np.pi, 1000)
    x_circle = np.cos(theta)
    y_circle = np.sin(theta)
    axes[2].plot(x_circle, y_circle, 'k-', alpha=0.3)
    
    # 画直线 Y = 3X
    axes[2].plot([-1, 1], [-3, 3], 'r-', linewidth=2, label='Y = 3X')
    
    # 标记角度
    angle_3x = np.arctan(3)
    axes[2].plot([0, np.cos(angle_3x)], [0, np.sin(angle_3x)], 'b-', linewidth=2)
    axes[2].text(0.3, 0.2, f'arctan(3)≈{np.degrees(angle_3x):.1f}°', fontsize=10)
    
    # 标记区域
    theta_upper = np.linspace(angle_3x, np.pi + angle_3x, 100)
    axes[2].fill_between(np.cos(theta_upper), np.sin(theta_upper), alpha=0.3, color='red', label='Y > 3X')
    
    axes[2].set_xlim(-1.5, 1.5)
    axes[2].set_ylim(-1.5, 1.5)
    axes[2].set_xlabel('X')
    axes[2].set_ylabel('Y')
    axes[2].set_title('几何解释')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    axes[2].set_aspect('equal')
    
    plt.tight_layout()
    plt.show()

def verify_with_integration():
    """用数值积分验证"""
    
    from scipy.integrate import dblquad
    
    def joint_pdf(y, x):
        """联合概率密度函数"""
        return (1/(2*np.pi)) * np.exp(-(x**2 + y**2)/2)
    
    # 计算 P(Y > 3X, X > 0)
    def integrand(y, x):
        if y > 3*x and x > 0:
            return joint_pdf(y, x)
        return 0
    
    # 数值积分（需要设定积分区域）
    try:
        result, error = dblquad(
            lambda y, x: joint_pdf(y, x),
            0, 5,  # x 范围
            lambda x: 3*x, lambda x: 5  # y 范围：从 3x 到 5
        )
        
        prob_conditional = result / 0.5  # 除以 P(X > 0) = 0.5
        
        print(f"\n数值积分验证:")
        print(f"P(Y > 3X, X > 0) ≈ {result:.6f}")
        print(f"P(Y > 3X | X > 0) ≈ {prob_conditional:.6f}")
        
        # 理论值
        theory = (np.pi/2 - np.arctan(3)) / np.pi
        print(f"理论值: {theory:.6f}")
        print(f"误差: {abs(prob_conditional - theory):.6f}")
        
    except Exception as e:
        print(f"数值积分计算出错: {e}")

# 运行所有分析
X, Y, prob1_sim, prob2_sim = monte_carlo_simulation()
prob2_theory = analytical_calculation()
visualize_problem(X, Y)
verify_with_integration()
```

## 答案

### P(Y > 3X) = 0.5

**解释：**
使用变量变换 Z = Y - 3X：
- E[Z] = 0
- Var(Z) = Var(Y) + 9Var(X) = 1 + 9 = 10
- Z ~ N(0, 10)
- P(Y > 3X) = P(Z > 0) = 0.5

### P(Y > 3X | X > 0) = (π/2 - arctan(3))/π ≈ 0.1024

**解释：**
使用几何方法：
- 在第一象限中，直线 Y = 3X 的角度为 arctan(3)
- 条件区域的角度跨度为 π/2 - arctan(3)
- 第一象限的总角度为 π/2
- 因此条件概率为 (π/2 - arctan(3))/(π/2) × (1/2) / (1/2) = (π/2 - arctan(3))/π

**数值结果：**
- arctan(3) ≈ 1.2490 弧度 ≈ 71.57°
- P(Y > 3X | X > 0) ≈ 0.1024
