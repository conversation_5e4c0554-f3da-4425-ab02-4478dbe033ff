# 问题9：最小值问题的期望

## 题目
X_i i.i.d. uniform ~[0,1]. You get them one by one. Ask about the expected number of X_i to see so that you get an X_i that is not the smallest among the first i random variables

## 解答

### 问题理解

**设定：**
- X₁, X₂, X₃, ... 独立同分布，服从 Uniform[0,1]
- 逐个观察这些随机变量
- 定义事件：Xᵢ 不是前i个随机变量中的最小值
- 求：第一次发生此事件的期望时间

**数学表述：**
定义 N = min{i ≥ 1 : Xᵢ ≠ min{X₁, X₂, ..., Xᵢ}}

求 E[N]

### 分析思路

**关键观察：**
- 当 i = 1 时，X₁ 总是最小值（也是唯一值）
- 当 i = 2 时，X₂ 不是最小值当且仅当 X₂ > X₁
- 当 i = 3 时，X₃ 不是最小值当且仅当 X₃ > min{X₁, X₂}
- 一般地，Xᵢ 不是最小值当且仅当 Xᵢ > min{X₁, ..., Xᵢ₋₁}

### 方法1：直接概率计算

**第i步成功的概率：**
P(Xᵢ 不是前i个中的最小值) = P(Xᵢ > min{X₁, ..., Xᵢ₋₁})

**关键洞察：**
对于连续分布，由于对称性：
P(Xᵢ = min{X₁, ..., Xᵢ}) = 1/i

因此：
P(Xᵢ ≠ min{X₁, ..., Xᵢ}) = 1 - 1/i = (i-1)/i

**期望计算：**
```
E[N] = Σ(i=1 to ∞) P(N ≥ i)
     = Σ(i=1 to ∞) P(前i-1步都失败)
     = Σ(i=1 to ∞) ∏(j=1 to i-1) P(第j步失败)
```

其中 P(第j步失败) = P(Xⱼ = min{X₁, ..., Xⱼ}) = 1/j

### 方法2：递推分析

**状态定义：**
设在第i步之前，当前最小值为m。

**转移分析：**
- 如果 Xᵢ < m，则Xᵢ成为新的最小值（失败）
- 如果 Xᵢ ≥ m，则Xᵢ不是最小值（成功）

**概率计算：**
P(Xᵢ ≥ m | 当前最小值为m) = 1 - m

但这需要考虑m的分布，比较复杂。

### 方法3：顺序统计量方法

**更直接的方法：**

P(N = k) = P(X₁, X₂, ..., Xₖ₋₁ 都是各自步骤的最小值，且 Xₖ 不是)

```
P(N = k) = (1/1) × (1/2) × ... × (1/(k-1)) × (1 - 1/k)
         = (1/(k-1)!) × ((k-1)/k)
         = (k-1)/(k × (k-1)!)
         = 1/(k-1)! - 1/(k!)
```

**期望计算：**
```
E[N] = Σ(k=2 to ∞) k × P(N = k)
     = Σ(k=2 to ∞) k × [1/(k-1)! - 1/k!]
     = Σ(k=2 to ∞) [k/(k-1)! - k/k!]
     = Σ(k=2 to ∞) [k/(k-1)! - 1/(k-1)!]
     = Σ(k=2 to ∞) [(k-1)/(k-1)!]
     = Σ(j=1 to ∞) j/j!  (令j = k-1)
```

**关键恒等式：**
```
Σ(j=1 to ∞) j/j! = Σ(j=1 to ∞) 1/(j-1)! = Σ(i=0 to ∞) 1/i! = e
```

因此：**E[N] = e**

### Python验证

```python
import numpy as np
import matplotlib.pyplot as plt

def simulate_minimum_problem(trials=100000):
    """蒙特卡洛模拟"""
    
    stopping_times = []
    
    for trial in range(trials):
        values = []
        i = 1
        
        while True:
            # 生成第i个随机数
            xi = np.random.uniform(0, 1)
            values.append(xi)
            
            # 检查xi是否是前i个中的最小值
            current_min = min(values)
            
            if xi != current_min:  # xi不是最小值
                stopping_times.append(i)
                break
            
            i += 1
            
            # 防止无限循环（理论上不会发生）
            if i > 100:
                stopping_times.append(i)
                break
    
    return stopping_times

def analyze_simulation():
    """分析模拟结果"""
    
    print("最小值问题的期望分析")
    print("=" * 50)
    
    # 运行模拟
    stopping_times = simulate_minimum_problem(100000)
    
    # 统计分析
    mean_time = np.mean(stopping_times)
    std_time = np.std(stopping_times)
    
    print(f"模拟次数: {len(stopping_times):,}")
    print(f"平均停止时间: {mean_time:.6f}")
    print(f"理论期望: {np.e:.6f}")
    print(f"误差: {abs(mean_time - np.e):.6f}")
    print(f"标准差: {std_time:.6f}")
    
    # 分布分析
    unique_times, frequencies = np.unique(stopping_times, return_counts=True)
    probabilities = frequencies / len(stopping_times)
    
    print(f"\n停止时间分布（前10个）:")
    for i in range(min(10, len(unique_times))):
        n = unique_times[i]
        prob_sim = probabilities[i]
        
        # 理论概率
        if n >= 2:
            prob_theory = 1/np.math.factorial(n-1) - 1/np.math.factorial(n)
        else:
            prob_theory = 0
        
        print(f"N = {n}: 模拟 = {prob_sim:.4f}, 理论 = {prob_theory:.4f}")
    
    return stopping_times

def theoretical_calculation():
    """理论计算验证"""
    
    print("\n理论计算")
    print("=" * 20)
    
    print("P(N = k) = 1/(k-1)! - 1/k!")
    print("E[N] = Σ(k=2 to ∞) k × P(N = k)")
    
    # 计算前几项
    expected_value = 0
    print(f"\n逐项计算:")
    
    for k in range(2, 15):
        prob_k = 1/np.math.factorial(k-1) - 1/np.math.factorial(k)
        contribution = k * prob_k
        expected_value += contribution
        
        print(f"k={k:2d}: P(N={k}) = {prob_k:.6f}, 贡献 = {contribution:.6f}")
    
    print(f"\n前13项和: {expected_value:.6f}")
    print(f"理论值 e: {np.e:.6f}")
    print(f"收敛误差: {abs(expected_value - np.e):.6f}")
    
    # 验证恒等式
    print(f"\n验证恒等式 Σ j/j! = e:")
    series_sum = sum(j/np.math.factorial(j) for j in range(1, 20))
    print(f"Σ(j=1 to 19) j/j! = {series_sum:.6f}")
    print(f"e = {np.e:.6f}")

def visualize_results(stopping_times):
    """可视化结果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 直方图
    max_time = min(max(stopping_times), 20)  # 限制显示范围
    bins = range(2, max_time + 2)
    
    axes[0,0].hist(stopping_times, bins=bins, density=True, alpha=0.7, 
                   color='lightblue', edgecolor='black', label='模拟')
    
    # 理论概率
    k_values = range(2, max_time + 1)
    theoretical_probs = [1/np.math.factorial(k-1) - 1/np.math.factorial(k) for k in k_values]
    
    axes[0,0].plot(k_values, theoretical_probs, 'ro-', linewidth=2, markersize=6, label='理论')
    axes[0,0].axvline(np.mean(stopping_times), color='red', linestyle='--', 
                      label=f'模拟均值: {np.mean(stopping_times):.3f}')
    axes[0,0].axvline(np.e, color='green', linestyle='--', 
                      label=f'理论期望: e = {np.e:.3f}')
    
    axes[0,0].set_xlabel('停止时间')
    axes[0,0].set_ylabel('概率')
    axes[0,0].set_title('停止时间分布')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 累积分布
    sorted_times = np.sort(stopping_times)
    cumulative = np.arange(1, len(sorted_times) + 1) / len(sorted_times)
    axes[0,1].plot(sorted_times, cumulative, linewidth=2, label='模拟CDF')
    
    # 理论累积分布
    k_range = range(2, 21)
    theoretical_cdf = []
    cumsum = 0
    for k in k_range:
        prob_k = 1/np.math.factorial(k-1) - 1/np.math.factorial(k)
        cumsum += prob_k
        theoretical_cdf.append(cumsum)
    
    axes[0,1].plot(k_range, theoretical_cdf, 'ro-', linewidth=2, markersize=4, label='理论CDF')
    axes[0,1].set_xlabel('停止时间')
    axes[0,1].set_ylabel('累积概率')
    axes[0,1].set_title('累积分布函数')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 收敛性分析
    running_mean = np.cumsum(stopping_times) / np.arange(1, len(stopping_times) + 1)
    axes[1,0].plot(running_mean[:10000], linewidth=1)
    axes[1,0].axhline(np.e, color='red', linestyle='--', linewidth=2, 
                      label=f'理论期望: e = {np.e:.3f}')
    axes[1,0].set_xlabel('模拟次数')
    axes[1,0].set_ylabel('累积平均值')
    axes[1,0].set_title('收敛性分析（前10000次）')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 理论解释
    axes[1,1].text(0.1, 0.9, '问题分析', fontsize=16, fontweight='bold')
    axes[1,1].text(0.1, 0.8, '• Xᵢ 不是前i个中的最小值', fontsize=12)
    axes[1,1].text(0.1, 0.7, '• P(Xᵢ = min) = 1/i', fontsize=12)
    axes[1,1].text(0.1, 0.6, '• P(N = k) = 1/(k-1)! - 1/k!', fontsize=12)
    axes[1,1].text(0.1, 0.5, '• E[N] = Σ j/j! = e', fontsize=12, color='red', fontweight='bold')
    axes[1,1].text(0.1, 0.4, '相关概念:', fontsize=14, fontweight='bold')
    axes[1,1].text(0.1, 0.3, '• 顺序统计量', fontsize=10)
    axes[1,1].text(0.1, 0.25, '• 停时问题', fontsize=10)
    axes[1,1].text(0.1, 0.2, '• 几何级数', fontsize=10)
    axes[1,1].text(0.1, 0.15, '• 指数函数展开', fontsize=10)
    axes[1,1].set_xlim(0, 1)
    axes[1,1].set_ylim(0, 1)
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.show()

def step_by_step_example():
    """逐步示例"""
    
    print("\n逐步示例")
    print("=" * 20)
    
    np.random.seed(42)
    
    print("模拟一次完整过程:")
    values = []
    
    for i in range(1, 10):
        xi = np.random.uniform(0, 1)
        values.append(xi)
        current_min = min(values)
        is_minimum = (xi == current_min)
        
        print(f"步骤 {i}: X_{i} = {xi:.4f}, 当前最小值 = {current_min:.4f}, "
              f"X_{i}是最小值: {is_minimum}")
        
        if not is_minimum:
            print(f"*** 在第 {i} 步首次出现非最小值! ***")
            break

# 运行所有分析
stopping_times = analyze_simulation()
theoretical_calculation()
visualize_results(stopping_times)
step_by_step_example()
```

## 答案

**期望停止时间：E[N] = e ≈ 2.718282**

**详细分析：**

1. **概率公式**：P(N = k) = 1/(k-1)! - 1/k!

2. **期望计算**：
   ```
   E[N] = Σ(k=2 to ∞) k × P(N = k) = Σ(j=1 to ∞) j/j! = e
   ```

3. **直觉解释**：
   - 第1步：X₁总是最小值
   - 第2步：X₂不是最小值的概率为1/2
   - 第k步：Xₖ不是最小值的概率为(k-1)/k
   - 平均需要约2.72步才能看到第一个非最小值

4. **关键洞察**：这个结果再次出现自然常数e，体现了概率论中的深层联系

**相关概念**：顺序统计量、停时理论、指数函数的级数展开
