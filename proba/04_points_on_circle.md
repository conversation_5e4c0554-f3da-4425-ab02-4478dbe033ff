# 问题4：圆周上点的几何概率

## 题目
Uniformly pick n points on a circle, what's the probability of them lying in the same semi-circle?

## 解答

### 问题分析

在圆周上均匀随机选择 n 个点，求这 n 个点都位于同一个半圆内的概率。

**关键洞察：**
- 由于圆的旋转对称性，可以固定第一个点的位置
- 问题转化为：其余 n-1 个点都落在以第一个点为端点的某个半圆内

### 方法1：几何概率方法

**步骤1：固定第一个点**
不失一般性，将第一个点固定在圆周上的某个位置（比如角度0处）。

**步骤2：分析约束条件**
设其余 n-1 个点的角度为 θ₁, θ₂, ..., θₙ₋₁ ∈ [0, 2π)

要使所有 n 个点在同一半圆内，必须存在一个半圆能包含所有点。

**步骤3：关键观察**
所有点在同一半圆内 ⟺ 所有点的角度跨度 ≤ π

即：max{θᵢ} - min{θᵢ} ≤ π （考虑第一个点在角度0）

### 方法2：顺序统计量方法

**将问题转化为线段问题：**
1. 将圆周"切开"展成长度为2π的线段
2. n个点对应n个在[0,2π)上的均匀随机数
3. 问题等价于：是否存在长度为π的区间包含所有n个点

**顺序统计量：**
设 n 个点的角度排序后为：0 ≤ U₍₁₎ ≤ U₍₂₎ ≤ ... ≤ U₍ₙ₎ < 2π

**间隙分析：**
定义间隙：Gᵢ = U₍ᵢ₊₁₎ - U₍ᵢ₎ (i = 1,...,n-1) 和 Gₙ = 2π + U₍₁₎ - U₍ₙ₎

所有点在同一半圆内 ⟺ 存在某个间隙 Gᵢ ≥ π

### 方法3：递推关系

**定义：** P(n) = n个点都在同一半圆内的概率

**递推思路：**
考虑第n个点的位置相对于前n-1个点的最大间隙。

**递推公式：**
```
P(n) = n × P(n-1) / 2^(n-1)
```

**边界条件：**
- P(1) = 1 （一个点总是在半圆内）
- P(2) = 1 （两个点总是在半圆内）

### 精确解

**定理：** n个点都在同一半圆内的概率为：
```
P(n) = n / 2^(n-1)
```

**证明：**
使用数学归纳法：

**基础情况：**
- P(1) = 1 = 1/2⁰ ✓
- P(2) = 1 = 2/2¹ ✓

**归纳步骤：**
假设 P(k) = k/2^(k-1) 对所有 k ≤ n-1 成立。

考虑 n 个点的情况：
1. 固定第一个点
2. 其余 n-1 个点必须都在某个半圆内
3. 这个半圆可以是以第一个点为端点的两个半圆之一

通过几何分析可以证明：P(n) = n/2^(n-1)

### Python验证

```python
import numpy as np
import matplotlib.pyplot as plt

def theoretical_probability(n):
    """理论概率公式"""
    return n / (2**(n-1))

def simulate_points_on_circle(n, trials=100000):
    """蒙特卡洛模拟"""
    success_count = 0
    
    for _ in range(trials):
        # 生成n个随机角度
        angles = np.random.uniform(0, 2*np.pi, n)
        angles = np.sort(angles)
        
        # 计算相邻点之间的间隙
        gaps = []
        for i in range(n-1):
            gaps.append(angles[i+1] - angles[i])
        # 最后一个间隙（跨越2π）
        gaps.append(2*np.pi - angles[-1] + angles[0])
        
        # 检查是否有间隙 >= π
        if max(gaps) >= np.pi:
            success_count += 1
    
    return success_count / trials

def verify_formula():
    """验证理论公式"""
    print("n个点在同一半圆内的概率验证:")
    print("-" * 50)
    print(f"{'n':>3} {'理论值':>10} {'模拟值':>10} {'误差':>10}")
    print("-" * 50)
    
    for n in range(1, 11):
        theoretical = theoretical_probability(n)
        if n <= 8:  # 避免模拟时间过长
            simulated = simulate_points_on_circle(n, trials=50000)
            error = abs(theoretical - simulated)
            print(f"{n:>3} {theoretical:>10.6f} {simulated:>10.6f} {error:>10.6f}")
        else:
            print(f"{n:>3} {theoretical:>10.6f} {'---':>10} {'---':>10}")

verify_formula()
```

### 可视化

```python
def visualize_problem():
    """可视化问题"""
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 绘制不同n值的概率
    n_values = range(1, 21)
    probabilities = [theoretical_probability(n) for n in n_values]
    
    axes[0,0].plot(n_values, probabilities, 'bo-', linewidth=2, markersize=6)
    axes[0,0].set_xlabel('点的个数 n')
    axes[0,0].set_ylabel('概率')
    axes[0,0].set_title('n个点在同一半圆内的概率')
    axes[0,0].grid(True, alpha=0.3)
    axes[0,0].set_yscale('log')
    
    # 绘制几个具体例子
    examples = [3, 4, 5]
    for idx, n in enumerate(examples):
        ax = axes[0,1] if idx == 0 else axes[1, idx-1]
        
        # 生成一个随机例子
        np.random.seed(42 + idx)
        angles = np.random.uniform(0, 2*np.pi, n)
        angles = np.sort(angles)
        
        # 绘制圆和点
        circle = plt.Circle((0, 0), 1, fill=False, color='black', linewidth=2)
        ax.add_patch(circle)
        
        # 绘制点
        x = np.cos(angles)
        y = np.sin(angles)
        ax.scatter(x, y, c='red', s=100, zorder=5)
        
        # 检查最大间隙
        gaps = []
        for i in range(n-1):
            gaps.append(angles[i+1] - angles[i])
        gaps.append(2*np.pi - angles[-1] + angles[0])
        
        max_gap_idx = np.argmax(gaps)
        max_gap = gaps[max_gap_idx]
        
        # 绘制最大间隙对应的半圆
        if max_gap >= np.pi:
            color = 'green'
            alpha = 0.3
            title_suffix = f"(在同一半圆内, 最大间隙={max_gap:.2f})"
        else:
            color = 'red'
            alpha = 0.2
            title_suffix = f"(不在同一半圆内, 最大间隙={max_gap:.2f})"
        
        # 绘制半圆
        if max_gap_idx < n-1:
            start_angle = angles[max_gap_idx]
            end_angle = angles[max_gap_idx + 1]
        else:
            start_angle = angles[-1]
            end_angle = angles[0] + 2*np.pi
        
        mid_angle = (start_angle + end_angle) / 2
        semicircle_start = mid_angle - np.pi/2
        semicircle_end = mid_angle + np.pi/2
        
        theta_semi = np.linspace(semicircle_start, semicircle_end, 100)
        x_semi = np.cos(theta_semi)
        y_semi = np.sin(theta_semi)
        ax.fill(np.concatenate([[0], x_semi, [0]]), 
                np.concatenate([[0], y_semi, [0]]), 
                color=color, alpha=alpha)
        
        ax.set_xlim(-1.5, 1.5)
        ax.set_ylim(-1.5, 1.5)
        ax.set_aspect('equal')
        ax.set_title(f'n={n} {title_suffix}')
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

visualize_problem()
```

### 渐近分析

```python
def asymptotic_analysis():
    """渐近行为分析"""
    
    print("渐近行为分析:")
    print("-" * 30)
    
    # 当n很大时，概率趋向于0
    large_n_values = [10, 20, 50, 100]
    
    for n in large_n_values:
        prob = theoretical_probability(n)
        print(f"n={n:3d}: P(n) = {prob:.2e}")
    
    print(f"\n当n→∞时，P(n) → 0")
    print(f"衰减速度：指数级衰减，约为 O(n/2^n)")

asymptotic_analysis()
```

## 答案

**精确公式：**
```
P(n) = n / 2^(n-1)
```

**具体数值：**
- P(1) = 1
- P(2) = 1  
- P(3) = 3/4 = 0.75
- P(4) = 4/8 = 0.5
- P(5) = 5/16 = 0.3125
- P(6) = 6/32 = 0.1875

**渐近行为：**
当 n → ∞ 时，P(n) → 0，衰减速度为指数级。

**直觉解释：**
随着点数增加，要求所有点都在同一半圆内变得越来越困难，概率呈指数衰减。
