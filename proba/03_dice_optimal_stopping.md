# 问题3：最优停止策略 - 骰子游戏

## 题目
Given a fair 6-side dice, you can keep the value after rolling or you can roll one more time and keep the new value. What are your strategy and the corresponding expectation of reward?

## 解答

### 问题分析

这是一个经典的最优停止问题。我们需要决定：
- 第一次掷出某个数字时，是保留还是重新掷一次？
- 目标是最大化期望收益

### 动态规划方法

**状态定义：**
- 当前掷出数字为 k (k = 1,2,3,4,5,6)
- 还有一次重掷机会

**决策：**
- 保留当前数字 k，收益为 k
- 重掷一次，期望收益为 E[第二次掷骰子] = (1+2+3+4+5+6)/6 = 3.5

**最优策略：**
```
如果 k ≥ 3.5，则保留
如果 k < 3.5，则重掷
```

由于 k 必须是整数，所以：
- k = 1, 2, 3：重掷
- k = 4, 5, 6：保留

### 期望收益计算

**第一次掷骰子的期望收益：**

```
E[收益] = P(掷出1)×E[收益|掷出1] + P(掷出2)×E[收益|掷出2] + ... + P(掷出6)×E[收益|掷出6]
```

其中：
- P(掷出k) = 1/6 对所有 k
- E[收益|掷出k] = 3.5 当 k ∈ {1,2,3} (选择重掷)
- E[收益|掷出k] = k 当 k ∈ {4,5,6} (选择保留)

**计算：**
```
E[收益] = (1/6) × 3.5 + (1/6) × 3.5 + (1/6) × 3.5 + (1/6) × 4 + (1/6) × 5 + (1/6) × 6
        = (1/6) × (3.5 + 3.5 + 3.5 + 4 + 5 + 6)
        = (1/6) × 25
        = 25/6
        ≈ 4.167
```

### 严格的数学证明

**定理：** 最优阈值为 3.5

**证明：**
设阈值为 t，即当掷出数字 ≥ t 时保留，< t 时重掷。

期望收益为：
```
E(t) = Σ(k=1 to ⌊t⌋) (1/6) × 3.5 + Σ(k=⌈t⌉ to 6) (1/6) × k
```

为了最大化 E(t)，我们需要：
- 当 k < 3.5 时，重掷的期望收益 3.5 > k，所以应该重掷
- 当 k > 3.5 时，保留的收益 k > 3.5，所以应该保留
- 当 k = 3.5 时，两种选择等价

因此最优阈值为 3.5。

### Python实现和验证

```python
import random
import numpy as np

def optimal_strategy_simulation(trials=100000):
    """模拟最优策略"""
    
    total_reward = 0
    strategy_counts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0}
    reroll_counts = {1: 0, 2: 0, 3: 0}
    
    for _ in range(trials):
        first_roll = random.randint(1, 6)
        strategy_counts[first_roll] += 1
        
        if first_roll <= 3:
            # 重掷
            second_roll = random.randint(1, 6)
            total_reward += second_roll
            reroll_counts[first_roll] += 1
        else:
            # 保留
            total_reward += first_roll
    
    average_reward = total_reward / trials
    
    print(f"模拟次数: {trials}")
    print(f"平均收益: {average_reward:.4f}")
    print(f"理论期望: {25/6:.4f}")
    print(f"误差: {abs(average_reward - 25/6):.4f}")
    
    print("\n策略统计:")
    for k in range(1, 7):
        action = "重掷" if k <= 3 else "保留"
        print(f"掷出{k}: {strategy_counts[k]}次, 策略: {action}")
    
    return average_reward

def compare_strategies():
    """比较不同策略的期望收益"""
    
    strategies = {
        "总是保留": lambda x: x,
        "总是重掷": lambda x: 3.5,
        "阈值=2.5": lambda x: x if x >= 3 else 3.5,
        "阈值=3.5": lambda x: x if x >= 4 else 3.5,
        "阈值=4.5": lambda x: x if x >= 5 else 3.5,
        "总是重掷除了6": lambda x: x if x == 6 else 3.5
    }
    
    print("不同策略的期望收益比较:")
    print("-" * 40)
    
    for name, strategy in strategies.items():
        expected_reward = sum(strategy(k) for k in range(1, 7)) / 6
        print(f"{name:15}: {expected_reward:.4f}")

# 运行模拟
optimal_strategy_simulation()
print("\n" + "="*50 + "\n")
compare_strategies()
```

### 敏感性分析

```python
def threshold_analysis():
    """分析不同阈值的期望收益"""
    
    import matplotlib.pyplot as plt
    
    thresholds = np.arange(1, 7, 0.1)
    expected_rewards = []
    
    for t in thresholds:
        # 计算期望收益
        reward = 0
        for k in range(1, 7):
            if k < t:
                reward += 3.5  # 重掷
            else:
                reward += k    # 保留
        expected_rewards.append(reward / 6)
    
    # 绘图
    plt.figure(figsize=(10, 6))
    plt.plot(thresholds, expected_rewards, 'b-', linewidth=2)
    plt.axvline(x=3.5, color='r', linestyle='--', alpha=0.7, label='最优阈值=3.5')
    plt.axhline(y=25/6, color='g', linestyle='--', alpha=0.7, label=f'最大期望收益={25/6:.3f}')
    plt.xlabel('阈值')
    plt.ylabel('期望收益')
    plt.title('不同阈值下的期望收益')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.show()
    
    # 找到最优阈值
    max_idx = np.argmax(expected_rewards)
    optimal_threshold = thresholds[max_idx]
    max_reward = expected_rewards[max_idx]
    
    print(f"数值最优阈值: {optimal_threshold:.1f}")
    print(f"最大期望收益: {max_reward:.4f}")

threshold_analysis()
```

### 扩展：多次重掷机会

```python
def multi_reroll_strategy(max_rerolls=2):
    """分析有多次重掷机会时的最优策略"""
    
    # 动态规划：从后往前计算
    # V[i] = 还有i次重掷机会时的期望收益
    
    V = [0] * (max_rerolls + 1)
    V[0] = 3.5  # 没有重掷机会时，期望收益就是随机掷骰子的期望
    
    thresholds = [0] * (max_rerolls + 1)
    
    for i in range(1, max_rerolls + 1):
        # 计算还有i次机会时的期望收益
        V[i] = sum(max(k, V[i-1]) for k in range(1, 7)) / 6
        
        # 计算最优阈值
        for t in range(1, 7):
            if t >= V[i-1]:
                thresholds[i] = t
                break
        else:
            thresholds[i] = 7  # 总是重掷
    
    print("多次重掷机会分析:")
    for i in range(max_rerolls + 1):
        print(f"剩余{i}次机会: 期望收益={V[i]:.4f}, 最优阈值={thresholds[i]}")
    
    return V, thresholds

multi_reroll_strategy(3)
```

## 答案

**最优策略：**
- 掷出 1, 2, 3：重掷
- 掷出 4, 5, 6：保留

**期望收益：**
```
E[收益] = 25/6 ≈ 4.167
```

**策略解释：**
- 阈值为 3.5
- 当掷出的数字小于重掷的期望收益(3.5)时，选择重掷
- 当掷出的数字大于等于重掷的期望收益时，选择保留
- 这个策略比总是保留(期望3.5)或总是重掷(期望3.5)都要好
