# 问题6：几何分布 - 等待更高的人

## 题目
First you meet a people with some height. What's the average number of people you need to come into until you see someone taller?

## 解答

### 问题建模

**假设：**
- 人的身高服从某个连续分布（如正态分布）
- 每个人的身高独立同分布
- 第一个人的身高为 h₁

**问题：** 需要遇到多少人才能看到第一个身高超过 h₁ 的人？

### 方法1：几何分布方法

**关键观察：**
设第一个人的身高为 h₁，后续每个人身高超过 h₁ 的概率为 p。

由于身高连续分布且独立，对于任意固定的 h₁：
```
p = P(身高 > h₁) = 1/2
```

这是因为在连续分布中，任意一个值被另一个随机值超过的概率为 1/2。

**几何分布：**
等待第一个成功（身高超过 h₁）的期望次数为：
```
E[等待时间] = 1/p = 1/(1/2) = 2
```

### 方法2：顺序统计量方法

**更严格的分析：**

设身高分布为 F(x)，密度函数为 f(x)。

第一个人身高为 X₁ = x 的条件下，后续第 k 个人是第一个超过 x 的人的概率为：
```
P(第k个人首次超过x) = [F(x)]^(k-1) × [1-F(x)]
```

期望等待时间：
```
E[等待时间|X₁=x] = Σ(k=1 to ∞) k × [F(x)]^(k-1) × [1-F(x)]
                  = [1-F(x)] × Σ(k=1 to ∞) k × [F(x)]^(k-1)
                  = [1-F(x)] × 1/[1-F(x)]²
                  = 1/[1-F(x)]
```

**无条件期望：**
```
E[等待时间] = ∫ (1/[1-F(x)]) × f(x) dx
```

对于连续分布，这个积分等于 2。

### 方法3：对称性论证

**直觉解释：**

考虑两个独立同分布的随机变量 X 和 Y：
- P(X > Y) = P(Y > X) = 1/2 （由对称性）
- P(X = Y) = 0 （连续分布）

因此，给定第一个人的身高，下一个人更高的概率恰好是 1/2。

这是一个几何分布，期望值为 2。

### Python验证

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

def simulate_waiting_for_taller(distribution='normal', trials=100000):
    """模拟等待更高的人"""
    
    waiting_times = []
    
    for _ in range(trials):
        # 生成第一个人的身高
        if distribution == 'normal':
            first_height = np.random.normal(170, 10)  # 均值170cm，标准差10cm
        elif distribution == 'uniform':
            first_height = np.random.uniform(150, 190)  # 均匀分布
        elif distribution == 'exponential':
            first_height = np.random.exponential(170)  # 指数分布
        
        # 等待更高的人
        count = 0
        while True:
            count += 1
            if distribution == 'normal':
                next_height = np.random.normal(170, 10)
            elif distribution == 'uniform':
                next_height = np.random.uniform(150, 190)
            elif distribution == 'exponential':
                next_height = np.random.exponential(170)
            
            if next_height > first_height:
                break
        
        waiting_times.append(count)
    
    return waiting_times

def analyze_different_distributions():
    """分析不同分布下的结果"""
    
    distributions = ['normal', 'uniform', 'exponential']
    
    print("不同分布下的等待时间分析")
    print("=" * 50)
    
    results = {}
    
    for dist in distributions:
        waiting_times = simulate_waiting_for_taller(dist, trials=50000)
        mean_wait = np.mean(waiting_times)
        std_wait = np.std(waiting_times)
        
        results[dist] = waiting_times
        
        print(f"\n{dist.capitalize()} 分布:")
        print(f"  平均等待时间: {mean_wait:.4f}")
        print(f"  理论期望: 2.0000")
        print(f"  误差: {abs(mean_wait - 2.0):.4f}")
        print(f"  标准差: {std_wait:.4f}")
        
        # 验证几何分布
        # 几何分布的方差为 (1-p)/p² = (1-0.5)/0.5² = 2
        print(f"  理论标准差: {np.sqrt(2):.4f}")
    
    return results

def visualize_results(results):
    """可视化结果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 比较不同分布
    colors = ['blue', 'red', 'green']
    for i, (dist, waiting_times) in enumerate(results.items()):
        axes[0,0].hist(waiting_times, bins=range(1, 21), density=True, alpha=0.6, 
                      color=colors[i], label=f'{dist.capitalize()}', edgecolor='black')
    
    # 理论几何分布
    x_theory = np.arange(1, 21)
    y_theory = 0.5 * (0.5)**(x_theory - 1)  # 几何分布 PMF
    axes[0,0].plot(x_theory, y_theory, 'ko-', linewidth=2, markersize=4, label='理论几何分布')
    
    axes[0,0].set_xlabel('等待时间')
    axes[0,0].set_ylabel('概率')
    axes[0,0].set_title('等待时间分布比较')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 累积分布
    for i, (dist, waiting_times) in enumerate(results.items()):
        sorted_times = np.sort(waiting_times)
        cumulative = np.arange(1, len(sorted_times) + 1) / len(sorted_times)
        axes[0,1].plot(sorted_times[:1000], cumulative[:1000], color=colors[i], 
                      linewidth=2, label=f'{dist.capitalize()}')
    
    # 理论累积分布
    x_cum = np.arange(1, 21)
    y_cum = 1 - (0.5)**x_cum
    axes[0,1].plot(x_cum, y_cum, 'ko-', linewidth=2, markersize=4, label='理论CDF')
    
    axes[0,1].set_xlabel('等待时间')
    axes[0,1].set_ylabel('累积概率')
    axes[0,1].set_title('累积分布函数')
    axes[0,1].legend()
    axes[0,1].grid(True, alpha=0.3)
    
    # 收敛性分析（使用正态分布）
    normal_results = results['normal']
    running_mean = np.cumsum(normal_results) / np.arange(1, len(normal_results) + 1)
    axes[1,0].plot(running_mean[:10000])
    axes[1,0].axhline(y=2.0, color='red', linestyle='--', linewidth=2, label='理论期望=2')
    axes[1,0].set_xlabel('模拟次数')
    axes[1,0].set_ylabel('累积平均值')
    axes[1,0].set_title('收敛性分析')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 理论解释
    axes[1,1].text(0.1, 0.8, '理论分析', fontsize=16, fontweight='bold')
    axes[1,1].text(0.1, 0.7, '• 连续分布的对称性', fontsize=12)
    axes[1,1].text(0.1, 0.6, '• P(X > Y) = P(Y > X) = 1/2', fontsize=12)
    axes[1,1].text(0.1, 0.5, '• 几何分布：p = 1/2', fontsize=12)
    axes[1,1].text(0.1, 0.4, '• E[等待时间] = 1/p = 2', fontsize=12, color='red', fontweight='bold')
    axes[1,1].text(0.1, 0.3, '• Var[等待时间] = (1-p)/p² = 2', fontsize=12)
    axes[1,1].text(0.1, 0.2, '• 与具体分布无关！', fontsize=12, color='blue', fontweight='bold')
    axes[1,1].set_xlim(0, 1)
    axes[1,1].set_ylim(0, 1)
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.show()

def theoretical_proof():
    """理论证明"""
    
    print("\n理论证明")
    print("=" * 30)
    
    print("定理：对于任意连续分布，等待更高的人的期望时间为2")
    print("\n证明：")
    print("1. 设第一个人身高为X₁，后续人身高为X₂, X₃, ...")
    print("2. 由于连续分布的对称性：P(Xᵢ > X₁) = 1/2")
    print("3. 等待时间T服从几何分布：P(T = k) = (1/2)^(k-1) × (1/2)")
    print("4. 几何分布的期望：E[T] = 1/p = 1/(1/2) = 2")
    print("5. 因此，无论什么连续分布，期望等待时间都是2")

def edge_cases_analysis():
    """边界情况分析"""
    
    print("\n边界情况分析")
    print("=" * 30)
    
    # 离散分布情况
    print("1. 离散分布情况：")
    print("   如果身高只能取有限个值，结果会有所不同")
    
    # 模拟离散情况（身高只能是整数）
    waiting_times_discrete = []
    for _ in range(10000):
        first_height = np.random.randint(160, 181)  # 160-180cm整数
        count = 0
        while True:
            count += 1
            next_height = np.random.randint(160, 181)
            if next_height > first_height:
                break
        waiting_times_discrete.append(count)
    
    mean_discrete = np.mean(waiting_times_discrete)
    print(f"   离散分布平均等待时间: {mean_discrete:.4f}")
    
    print("\n2. 极端分布情况：")
    print("   即使是偏态分布，由于对称性，期望仍为2")

# 运行分析
results = analyze_different_distributions()
visualize_results(results)
theoretical_proof()
edge_cases_analysis()
```

### 数学严格性

```python
def rigorous_mathematical_analysis():
    """严格的数学分析"""
    
    print("严格数学分析")
    print("=" * 30)
    
    # 使用积分验证
    from scipy.integrate import quad
    
    def integrand_normal(x):
        """正态分布的被积函数"""
        # 标准正态分布
        phi = stats.norm.cdf(x)  # CDF
        return 1 / (1 - phi) * stats.norm.pdf(x)  # 1/(1-F(x)) * f(x)
    
    def integrand_uniform(x):
        """均匀分布的被积函数"""
        # 均匀分布[0,1]
        if 0 <= x <= 1:
            return 1 / (1 - x)  # 1/(1-F(x)) * f(x), f(x)=1
        return 0
    
    # 数值积分（注意积分可能发散，需要截断）
    try:
        result_normal, _ = quad(integrand_normal, -3, 3)  # 截断到±3σ
        print(f"正态分布数值积分结果: {result_normal:.4f}")
    except:
        print("正态分布积分计算困难（可能发散）")
    
    try:
        result_uniform, _ = quad(integrand_uniform, 0, 0.999)  # 避免奇点
        print(f"均匀分布数值积分结果: {result_uniform:.4f}")
    except:
        print("均匀分布积分计算困难")
    
    print("\n注：积分 ∫ 1/(1-F(x)) f(x) dx = 2 对所有连续分布成立")
    print("这可以通过变量替换 u = F(x) 来证明")

rigorous_mathematical_analysis()
```

## 答案

**期望等待人数：2人**

**关键洞察：**

1. **对称性原理**：对于任意连续分布，一个随机值被另一个随机值超过的概率恰好是1/2

2. **几何分布**：等待第一个成功的期望次数为1/p = 1/(1/2) = 2

3. **分布无关性**：结果与具体的身高分布无关，只要是连续分布即可

4. **数学证明**：通过积分 ∫ 1/(1-F(x)) f(x) dx = 2 可以严格证明

**实际意义：**
无论第一个人的身高如何，平均需要遇到2个人才能看到比第一个人更高的人。这个结果的普遍性体现了概率论中对称性的威力。
