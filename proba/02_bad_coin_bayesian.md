# 问题2：贝叶斯推断 - 坏硬币概率

## 题目
1000 coins, 1 bad coin (HH), flip 10 H in a roll, what's the probability of getting the bad coin?
有1000枚硬币，其中1枚是坏硬币（总是正面），连续抛出10个正面，求拿到坏硬币的概率是多少？
## 解答

### 问题理解
- 有1000枚硬币，其中999枚是公平硬币，1枚是坏硬币（只能抛出正面H）
- 随机选择一枚硬币，连续抛出10个正面
- 求这枚硬币是坏硬币的概率

### 贝叶斯定理应用

**定义事件：**
- B: 选中的是坏硬币
- A: 连续抛出10个正面

**已知信息：**
- P(B) = 1/1000 (先验概率)
- P(B^c) = 999/1000
- P(A|B) = 1 (坏硬币必然抛出正面)
- P(A|B^c) = (1/2)^10 = 1/1024 (公平硬币连续10个正面的概率)

**贝叶斯公式：**
```
P(B|A) = P(A|B) × P(B) / P(A)
```

其中：
```
P(A) = P(A|B) × P(B) + P(A|B^c) × P(B^c)
     = 1 × (1/1000) + (1/1024) × (999/1000)
     = 1/1000 + 999/(1000×1024)
     = 1/1000 + 999/1024000
```

### 详细计算

**计算P(A)：**
```
P(A) = 1/1000 + 999/1024000
     = 1024/1024000 + 999/1024000
     = (1024 + 999)/1024000
     = 2023/1024000
```

**计算P(B|A)：**
```
P(B|A) = P(A|B) × P(B) / P(A)
       = 1 × (1/1000) / (2023/1024000)
       = (1/1000) × (1024000/2023)
       = 1024000/(1000 × 2023)
       = 1024/2023
```

### 数值结果

```python
def calculate_bad_coin_probability():
    """计算坏硬币的后验概率"""
    
    # 参数设置
    total_coins = 1000
    bad_coins = 1
    fair_coins = total_coins - bad_coins
    consecutive_heads = 10
    
    # 先验概率
    prior_bad = bad_coins / total_coins
    prior_fair = fair_coins / total_coins
    
    # 似然概率
    likelihood_bad = 1.0  # 坏硬币必然抛出正面
    likelihood_fair = (0.5) ** consecutive_heads  # 公平硬币连续10个正面
    
    # 边际概率
    marginal = likelihood_bad * prior_bad + likelihood_fair * prior_fair
    
    # 后验概率
    posterior_bad = (likelihood_bad * prior_bad) / marginal
    
    print(f"先验概率 P(坏硬币) = {prior_bad}")
    print(f"似然概率 P(10个H|坏硬币) = {likelihood_bad}")
    print(f"似然概率 P(10个H|公平硬币) = {likelihood_fair:.6f}")
    print(f"边际概率 P(10个H) = {marginal:.6f}")
    print(f"后验概率 P(坏硬币|10个H) = {posterior_bad:.6f}")
    print(f"精确分数形式: {1024}/{2023} ≈ {1024/2023:.6f}")
    
    return posterior_bad

result = calculate_bad_coin_probability()
```

### 直觉解释

**为什么概率这么高？**

1. **稀有事件的强证据**：连续10个正面是一个相对稀有的事件（概率约1/1024）
2. **坏硬币的完美解释**：坏硬币能完美解释这个观察结果
3. **贝叶斯更新**：虽然坏硬币的先验概率很低(0.1%)，但强证据大幅提升了后验概率

### 敏感性分析

```python
def sensitivity_analysis():
    """分析不同参数下的结果"""
    
    import matplotlib.pyplot as plt
    import numpy as np
    
    # 分析连续正面次数的影响
    heads_range = range(1, 21)
    probabilities = []
    
    for h in heads_range:
        prior_bad = 1/1000
        likelihood_bad = 1.0
        likelihood_fair = (0.5)**h
        
        marginal = likelihood_bad * prior_bad + likelihood_fair * (999/1000)
        posterior = (likelihood_bad * prior_bad) / marginal
        probabilities.append(posterior)
    
    # 绘图
    plt.figure(figsize=(10, 6))
    plt.plot(heads_range, probabilities, 'b-o', linewidth=2, markersize=6)
    plt.xlabel('连续正面次数')
    plt.ylabel('坏硬币的后验概率')
    plt.title('连续正面次数对坏硬币概率的影响')
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0.5, color='r', linestyle='--', alpha=0.7, label='50%阈值')
    plt.legend()
    plt.show()
    
    # 打印关键点
    for i, h in enumerate([5, 10, 15, 20]):
        if h <= 20:
            idx = h - 1
            print(f"{h}个连续正面: P(坏硬币|观察) = {probabilities[idx]:.4f}")

sensitivity_analysis()
```

### 蒙特卡洛验证

```python
import random

def monte_carlo_verification(trials=100000):
    """蒙特卡洛验证贝叶斯结果"""
    
    bad_coin_count = 0
    valid_trials = 0
    
    for _ in range(trials):
        # 随机选择硬币类型
        is_bad_coin = random.random() < 1/1000
        
        # 模拟抛硬币
        if is_bad_coin:
            # 坏硬币：总是正面
            sequence = [1] * 10
        else:
            # 公平硬币
            sequence = [random.randint(0, 1) for _ in range(10)]
        
        # 检查是否连续10个正面
        if all(x == 1 for x in sequence):
            valid_trials += 1
            if is_bad_coin:
                bad_coin_count += 1
    
    if valid_trials > 0:
        simulated_prob = bad_coin_count / valid_trials
        theoretical_prob = 1024/2023
        
        print(f"模拟试验次数: {trials}")
        print(f"有效试验次数: {valid_trials}")
        print(f"模拟概率: {simulated_prob:.4f}")
        print(f"理论概率: {theoretical_prob:.4f}")
        print(f"误差: {abs(simulated_prob - theoretical_prob):.4f}")
    else:
        print("没有观察到连续10个正面的情况")

monte_carlo_verification()
```

## 答案

**坏硬币的概率为：**
```
P(坏硬币|10个连续正面) = 1024/2023 ≈ 0.5062 ≈ 50.62%
```

**关键洞察：**
- 尽管坏硬币的先验概率只有0.1%，但连续10个正面这一强证据将后验概率提升到约50.6%
- 这展示了贝叶斯推断在稀有事件检测中的威力
