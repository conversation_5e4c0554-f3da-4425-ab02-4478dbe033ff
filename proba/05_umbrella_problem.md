# 问题5：雨伞问题的马尔可夫链分析

## 题目
Let's say you have two umbrellas at home and every day, you need to travel between work and home. During each trip, there is a 50% chance of raining. If it is raining when you leave your house and you have an umbrella with you, you take the umbrella to your office (similarly for office to home). Otherwise, you get drenched. What's the expected number of trips until you are drenched? Simulate this.

## 解答

### 问题建模

**状态空间：**
- (i, j)：家里有i把伞，办公室有j把伞，其中i + j = 2

**可能状态：**
- (2, 0)：两把伞都在家
- (1, 1)：家里和办公室各一把伞  
- (0, 2)：两把伞都在办公室

**转移规则：**
- 下雨概率：p = 0.5
- 如果下雨且当前位置有伞：带伞到目的地
- 如果下雨且当前位置无伞：被淋湿（游戏结束）
- 如果不下雨：不带伞

### 马尔可夫链分析

**状态转移图：**

从家出发：
- (2,0) → (1,1) 概率0.5（下雨，带伞）
- (2,0) → (2,0) 概率0.5（不下雨）
- (1,1) → (0,2) 概率0.5（下雨，带伞）
- (1,1) → (1,1) 概率0.5（不下雨）
- (0,2) → 被淋湿 概率0.5（下雨，无伞）
- (0,2) → (0,2) 概率0.5（不下雨）

从办公室回家的转移类似，只是方向相反。

### 期望计算

**定义变量：**
- E₂₀：从状态(2,0)开始的期望步数
- E₁₁：从状态(1,1)开始的期望步数  
- E₀₂：从状态(0,2)开始的期望步数

**方程组：**
```
E₂₀ = 1 + 0.5 × E₁₁ + 0.5 × E₂₀
E₁₁ = 1 + 0.5 × E₀₂ + 0.5 × E₁₁  
E₀₂ = 1 + 0.5 × 0 + 0.5 × E₀₂
```

**求解：**
从第三个方程：
```
E₀₂ = 1 + 0.5 × E₀₂
0.5 × E₀₂ = 1
E₀₂ = 2
```

从第二个方程：
```
E₁₁ = 1 + 0.5 × 2 + 0.5 × E₁₁
0.5 × E₁₁ = 2
E₁₁ = 4
```

从第一个方程：
```
E₂₀ = 1 + 0.5 × 4 + 0.5 × E₂₀
0.5 × E₂₀ = 3
E₂₀ = 6
```

### Python模拟

```python
import random
import numpy as np
import matplotlib.pyplot as plt

class UmbrellaSimulation:
    def __init__(self):
        self.home_umbrellas = 2
        self.office_umbrellas = 0
        self.location = 'home'  # 'home' or 'office'
        self.trips = 0
        self.drenched = False
    
    def reset(self):
        """重置模拟状态"""
        self.home_umbrellas = 2
        self.office_umbrellas = 0
        self.location = 'home'
        self.trips = 0
        self.drenched = False
    
    def make_trip(self):
        """执行一次出行"""
        self.trips += 1
        is_raining = random.random() < 0.5
        
        if self.location == 'home':
            # 从家到办公室
            if is_raining:
                if self.home_umbrellas > 0:
                    # 有伞，带走
                    self.home_umbrellas -= 1
                    self.office_umbrellas += 1
                else:
                    # 没伞，被淋湿
                    self.drenched = True
                    return
            # 不下雨或带了伞，到达办公室
            self.location = 'office'
        
        else:
            # 从办公室到家
            if is_raining:
                if self.office_umbrellas > 0:
                    # 有伞，带走
                    self.office_umbrellas -= 1
                    self.home_umbrellas += 1
                else:
                    # 没伞，被淋湿
                    self.drenched = True
                    return
            # 不下雨或带了伞，到达家
            self.location = 'home'
    
    def simulate_until_drenched(self):
        """模拟直到被淋湿"""
        self.reset()
        while not self.drenched:
            self.make_trip()
        return self.trips

def run_simulation(num_simulations=10000):
    """运行多次模拟"""
    simulator = UmbrellaSimulation()
    results = []
    
    for _ in range(num_simulations):
        trips = simulator.simulate_until_drenched()
        results.append(trips)
    
    return results

def analyze_results():
    """分析模拟结果"""
    print("雨伞问题模拟分析")
    print("=" * 50)
    
    # 运行模拟
    results = run_simulation(100000)
    
    # 统计分析
    mean_trips = np.mean(results)
    std_trips = np.std(results)
    median_trips = np.median(results)
    
    print(f"模拟次数: {len(results)}")
    print(f"平均出行次数: {mean_trips:.4f}")
    print(f"理论期望值: 6.0000")
    print(f"误差: {abs(mean_trips - 6.0):.4f}")
    print(f"标准差: {std_trips:.4f}")
    print(f"中位数: {median_trips}")
    
    # 分布分析
    unique_values, counts = np.unique(results, return_counts=True)
    probabilities = counts / len(results)
    
    print(f"\n出行次数分布（前10个）:")
    for i in range(min(10, len(unique_values))):
        print(f"{unique_values[i]:2d}次: {probabilities[i]:.4f}")
    
    return results

def visualize_results(results):
    """可视化结果"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 直方图
    axes[0,0].hist(results, bins=50, density=True, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].axvline(np.mean(results), color='red', linestyle='--', linewidth=2, label=f'模拟均值: {np.mean(results):.2f}')
    axes[0,0].axvline(6.0, color='green', linestyle='--', linewidth=2, label='理论期望: 6.0')
    axes[0,0].set_xlabel('出行次数')
    axes[0,0].set_ylabel('概率密度')
    axes[0,0].set_title('出行次数分布')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 累积分布
    sorted_results = np.sort(results)
    cumulative_prob = np.arange(1, len(sorted_results) + 1) / len(sorted_results)
    axes[0,1].plot(sorted_results, cumulative_prob, linewidth=2)
    axes[0,1].set_xlabel('出行次数')
    axes[0,1].set_ylabel('累积概率')
    axes[0,1].set_title('累积分布函数')
    axes[0,1].grid(True, alpha=0.3)
    
    # 收敛性分析
    running_mean = np.cumsum(results) / np.arange(1, len(results) + 1)
    axes[1,0].plot(running_mean[:10000], linewidth=1)
    axes[1,0].axhline(6.0, color='red', linestyle='--', label='理论期望: 6.0')
    axes[1,0].set_xlabel('模拟次数')
    axes[1,0].set_ylabel('累积平均值')
    axes[1,0].set_title('收敛性分析（前10000次）')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 状态转移示意图
    axes[1,1].text(0.1, 0.8, '状态转移图', fontsize=16, fontweight='bold')
    axes[1,1].text(0.1, 0.7, '(2,0) → (1,1): p=0.5', fontsize=12)
    axes[1,1].text(0.1, 0.6, '(1,1) → (0,2): p=0.5', fontsize=12)
    axes[1,1].text(0.1, 0.5, '(0,2) → 淋湿: p=0.5', fontsize=12)
    axes[1,1].text(0.1, 0.3, '期望出行次数:', fontsize=14, fontweight='bold')
    axes[1,1].text(0.1, 0.2, 'E[从(2,0)开始] = 6', fontsize=12, color='red')
    axes[1,1].text(0.1, 0.1, 'E[从(1,1)开始] = 4', fontsize=12, color='blue')
    axes[1,1].text(0.1, 0.0, 'E[从(0,2)开始] = 2', fontsize=12, color='green')
    axes[1,1].set_xlim(0, 1)
    axes[1,1].set_ylim(0, 1)
    axes[1,1].axis('off')
    
    plt.tight_layout()
    plt.show()

def detailed_state_analysis():
    """详细的状态分析"""
    
    print("\n详细状态分析")
    print("=" * 30)
    
    # 分别从不同初始状态开始模拟
    initial_states = [
        (2, 0, 'home'),
        (1, 1, 'home'), 
        (0, 2, 'home')
    ]
    
    for home_umbrellas, office_umbrellas, location in initial_states:
        results = []
        
        for _ in range(10000):
            simulator = UmbrellaSimulation()
            simulator.home_umbrellas = home_umbrellas
            simulator.office_umbrellas = office_umbrellas
            simulator.location = location
            simulator.trips = 0
            simulator.drenched = False
            
            while not simulator.drenched:
                simulator.make_trip()
            
            results.append(simulator.trips)
        
        mean_trips = np.mean(results)
        theoretical = [6, 4, 2][initial_states.index((home_umbrellas, office_umbrellas, location))]
        
        print(f"初始状态 ({home_umbrellas},{office_umbrellas}):")
        print(f"  模拟期望: {mean_trips:.4f}")
        print(f"  理论期望: {theoretical}")
        print(f"  误差: {abs(mean_trips - theoretical):.4f}")

# 运行分析
results = analyze_results()
visualize_results(results)
detailed_state_analysis()
```

### 理论验证

```python
def theoretical_verification():
    """理论计算验证"""
    
    print("\n理论计算验证")
    print("=" * 30)
    
    # 方程组求解
    # E₀₂ = 1 + 0.5 × E₀₂  =>  E₀₂ = 2
    E_02 = 2
    
    # E₁₁ = 1 + 0.5 × E₀₂ + 0.5 × E₁₁  =>  E₁₁ = 4  
    E_11 = 4
    
    # E₂₀ = 1 + 0.5 × E₁₁ + 0.5 × E₂₀  =>  E₂₀ = 6
    E_20 = 6
    
    print(f"从状态(0,2)开始的期望步数: {E_02}")
    print(f"从状态(1,1)开始的期望步数: {E_11}")
    print(f"从状态(2,0)开始的期望步数: {E_20}")
    
    # 验证方程
    print(f"\n方程验证:")
    print(f"E₀₂ = 1 + 0.5 × E₀₂: {1 + 0.5 * E_02} = {E_02}")
    print(f"E₁₁ = 1 + 0.5 × E₀₂ + 0.5 × E₁₁: {1 + 0.5 * E_02 + 0.5 * E_11} = {E_11}")
    print(f"E₂₀ = 1 + 0.5 × E₁₁ + 0.5 × E₂₀: {1 + 0.5 * E_11 + 0.5 * E_20} = {E_20}")

theoretical_verification()
```

## 答案

**期望出行次数：6次**

**详细分析：**
- 从状态(2,0)开始：期望6次出行
- 从状态(1,1)开始：期望4次出行  
- 从状态(0,2)开始：期望2次出行

**关键洞察：**
1. 这是一个吸收马尔可夫链问题
2. 状态转移概率为0.5（下雨概率）
3. 通过求解线性方程组得到精确解
4. 模拟结果验证了理论计算的正确性
