>Flora: 欢迎来到量化好声音，量化人说量化事儿。我是Flora

>Aaron: 大家好！我是Aaron

>Flora: 今天咱们来深入聊聊 强化学习 就是 RL，在量化交易中的应用
>Flora: 大家肯定听过强化学习 对吧?
>Flora: 像那个AlphaGo下围棋就是在用强化学习
>Flora: 但它用在金融市场这个玩法
>Flora: 可能比我们想的要有意思的多
>Flora: 我看资料里说 像摩根大通
>Flora: 他们已经正在用RL优化交易执行了

>Aaron: 是的,这说明它不是纯理论了
>Aaron: 开始有实际应用了

>Flora: 对 所以咱们这次的目标就是想帮大家呀
>Flora: 快速抓住这个强化学习
>Flora: 跟咱们熟悉的 比如说
>Flora: 预测股价涨跌的那种监督学习的
>Flora: 核心区别在哪？
>Flora: 还有就是为啥有人觉得
>Flora: 他在金融市场这种噪音特别大的地方
>Flora: 可能有优势？
>Flora: 还有，要想自己搭一个基础的RL交易模型
>Flora: 大概得分几步走
>Flora: 内容很多，我们就挑那些最核心、最关键的点来讲好了

>Aaron: 好嘞 那我们先打个比方吧 这样好理解
>Aaron: 你就想象你在玩一个
>Aaron: 策略类的电子游戏
>Aaron: 你呢就是那个玩家 我们的术语叫代理
>Aaron: Agent
>Aaron: 然后那个游戏世界就是环境 Environment，就是我们的市场
>Aaron: 你在游戏里看到的画面
>Aaron: 什么股价、交易量啊 各种指标
>Aaron: 这个就是状态state
>Aaron: 你的操作：买卖
>Aaron: 或者拿着不动 就是行动
>Aaron: 最后得分高低 那不就是你的投资收益吗

>Flora: 那这个就是奖励了吧？

>Aaron: 对,强化学习的核心简单说
>Aaron: 就是让你的交易程序
>Aaron: 就是那个代理在市场这个环境里自己去试
>Aaron: 对,不断尝试各种行动
>Aaron: 然后看结果是赚了还是赔了 拿到奖励或者惩罚
>Aaron: 然后他就从这里面学习

>Flora: 那目标是啥呢？

>Aaron: 目标就是找到一套策略，能让长期的总收益最大化

>Flora: 长期总收益，而不是预测次日涨跌？

>Aaron: 对，这是强化学习的关键点 
>Aaron: 他学的不是明天是涨还是跌 这种预测题

>Flora: 哦 那学什么

>Aaron: 他学的是现在这个状况下
>Aaron: 我下一步怎么做才是最好的选择
>Aaron: 他学的是一种动态的决策能力

>Flora: 这就有意思了 我们平时搞的监督学习
>Flora: 更像是拿着标准答案喂给模型
>Flora: 告诉他 这种情况后面大概率涨
>Flora: 那种情况后面大概率会跌，对吧？

>Aaron: 没错,监督学习需要有明确的正确答案
>Aaron: 就是标签

>Flora: 但强化学习好像没这个标准答案
>Flora: 他得自己摸索
>Flora: 那会不会试出来一堆很糟糕的策略？

>Aaron: 问到点子上了 
>Aaron: 这个就是强化学习里面一个特别核心的东西
>Aaron: 探索和利用

>Flora: 探索和利用？

>Aaron: 对，利用就是用他已经知道的
>Aaron: 好像还不错的策略去赚钱
>Aaron: 探索就是去尝试一些
>Aaron: 新的 没试过的操作
>Aaron: 看看效果怎么样 说不定能发现更好的呢 对吧？

>Flora: 嗯 有可能更好 也可能更糟

>Aaron: 是的,所以好的R算法L
>Aaron: 就要能在这两者之间
>Aaron: 找到一个比较好的平衡点 这让他有可能
>Aaron: 发现一些我们人想不到的
>Aaron: 甚至有点反直觉的交易模式

>Flora: 恩！这个是监督学习比较难做到的
>Flora: 监督学习只会学标准答案

>Aaron: 对 强化学习不依赖于过去的固定模式
>Aaron: 这个试错和看长期的能力在金融市场特别有用

>Flora: 对，因为金融数据里面噪声实在太多了
>Flora: 对！各种短期的随机波动特别干扰判断

>Aaron: 确实如此
>Aaron: 你想啊 如果
>Aaron: 监督学习模型的目标是每一步预测都准
>Aaron: 那就很容易被这些短期噪声带偏
>Aaron: 可能稍微波动一下他就要交易
>Aaron: 结果光手续费都亏死了

>Flora: 有道理

>Aaron: 但强化学习不一样
>Aaron: 它的目标是长期累积回报
>Aaron: 所以它更能容忍短期的波动
>Aaron: 甚至是暂时的亏损

>Flora: 怎么说？

>Aaron: 比如下跌时，只要它判断目前只是市场噪音 是假摔
>Aaron: 它反而可能在这个时候买入
>Aaron: 因为他看的更远

>Flora: 对的。再来说说资料里提到的
>Flora: 摩根大通的那个LOXM平台是怎么回事？

>Aaron: 啊，这是个很好的应用例子 它们用RL来智能拆分大单
>Aaron: 你想啊，在交易中，一个大单直接砸下去 那市场肯定成本就高了
>Aaron: LOXM就是
>Aaron: 使用RL在几千种可能的
>Aaron: 拆单和执行路径里动态地选目标
>Aaron: 就是悄悄的把单子执行完
>Aaron: 把这个冲击成本降到最低

>Flora: 听起来很复杂

>Aaron: 是挺复杂的
>Aaron: 还有一个叫Alpha Stock的模型
>Aaron: 也是用RL来优化咱们常说的追涨杀跌策略
>Aaron: 但他不是傻跟,是学习什么时候该追
>Aaron: 什么时候该停
>Aaron: 动态调整
>Aaron: 这种适应性是很多传统模型比较欠缺的

>Flora: 明白了 感觉他确实思路不一样
>Flora: 那如果我想自己动手搞一个强化学习策略
>Flora: 大概是个什么流程？

>Aaron: 正好！我们最近出了一个教程
>Aaron: 这个教程就在我们的研究平台上可以查看和运行

>Flora: 那这个就太方便学习了。我可以自己动手改改代码
>Flora: 再运行看看结果 对吧？

>Aaron: 对，正是这样。我们研究平台上的教程，都是可以执行的

>Flora: 那太好了，这样不仅确保了听众拿到的代码是能正确执行的。
>Flora: 还方便用户通过动手尝试加快学习进度。
>Flora: 那快给我们说说，这个量化交易系统是怎么搭建的呢？

>Aaron: 搭一个基础的RL交易系统 大概这么几步走
>Aaron: 第一步肯定是特征工程
>Aaron: 就是选择一些
>Aaron: 能描述市场状态的特征
>Aaron: 比如像RSI, MACD这些技术指标

>Flora: 对 这些是基础

>Aaron: 第二步就是构建那个交易环境
>Aaron: 这就像给你的代理搭了个模拟交易的沙盘

>Flora: 状态怎么表示

>Aaron: 状态就是用上一步的特征
>Aaron: 以及允许哪些操作，是买是卖，还持有量是多少等等

>Flora: 还有最重要的问题，是不是怎么算奖励？

>Aaron: 对，奖励机制直接决定了代理学啥
>Aaron: 比如每次交易赚多少亏多少
>Aaron: 持仓收益怎么算等等

>Flora: 恩。这是前两步，那第三步就是选择强化学习算法了吧？

>Aaron: 对，第三步就是选择算法，训练你的代理
>Aaron: 比如教程里重点讲的PPO算法
>Aaron: 可以用那个 Stable Bassline 3 库来实现

>Flora: 就是Stable Basslines 3中的PPO算法

>Aaron: 然后就让代理在模拟环境里跑
>Aaron: 跑大量的模拟交易 让他自己去学习
>Aaron: 这里有个细节， 
>Aaron: 我们教程里特别强调的那个n-steps参数

>Flora: 对,这个我知道。它关系到代理一次决策能看多远
>Flora: 是只看眼前一步？
>Flora: 还是能规划一个比较长周期的行动

>Aaron: 对！这个对学习长期策略
>Aaron: 避免短视特别关键

>Flora: 哦 这个得注意

>Aaron: 最后一步必不可少的就是回测评估
>Aaron: 代理训练好了
>Aaron: 不能光在模拟环境里说好,
>Aaron: 得拿到真实的、他没见过的历史数据上跑跑看

>Flora: 嗯,得实战检验。
>Flora: 那么跑出来的数据，我们怎么评价呢？

>Aaron: 这得用QuantStats这类工具
>Aaron: Quantstats能出很详细的回测报告
>Aaron: 还能跟基准比一比
>Aaron: 看看效果到底怎样

>Flora: 听起来步骤是清晰了
>Flora: 但感觉坑也不少吧

>Aaron: 确实 实际搞起来细节非常多
>Aaron: 比如
>Aaron: 量化交易这一块的强化学习模型
>Aaron: 本来大家最常用的是FinRL
>Aaron: 但在国内用 因为有依赖问题 所以用不了

>Flora: 哦, 具体是什么问题？

>Aaron: 比如FinRL依赖数据源YFinance

>Flora: 这个我知道，从2021年底，就不能在国内用了

>Aaron: 对，它还依赖了Alpaca这个交易API，这个是用于美股交易的

>Flora: 看起来是用不上。那么如果我不用它，会有什么问题吗？

>Aaron: 对，不用都不行，会有导入问题

>Flora: 哦, 所以看起来，得自己从头写
>Flora: 不过也不完全算是从头写，毕竟有gynasium和stable-baselines3可以用

>Aaron: 对，我们的模型正是在这两个库的基础上开发的

>Flora: 所以 其实我们这个模型还是很通用的。
>Flora: 除了这些，那还有别的坑吗？

>Aaron: 对，又比如，我们得保证训练和测试用的资产列表一样

>Flora: 这是不是说，如果我们是拿三个资产训练的
>Flora: 那么实盘时，也就只能对同样的三个资产进行预测，而不能扩展到其它资产上？

>Aaron: 对，正是这样
>Aaron: 这些工程上的细节特别特别关键
>Aaron: 有时候这些搞不好比算法本身影响还大

>Flora: 工程实现很重要对非常重要

>Aaron: 在量化交易实践中，
>Aaron: 动手能力和对算法的理解 这两种能力都很重要

>Flora: 好的 非常清楚了
>Flora: 总的来看,强化学习
>Flora: 给量化交易提供了一个不同的视角
>Flora: 它不是让机器单纯复制过去的模式

>Aaron: 对

>Flora: 而是让机器在跟市场的互动里自己学习
>Flora: 自己进化出那个
>Flora: 交易的智慧 是吧
>Flora: 同时，它更关注动态决策和长期回报

>Aaron: 正是这个意思 适应性动态决策和长期视角
>Aaron: 我这里还有一个挺有意思的问题 你可以琢磨琢磨

>Flora: 哦？愿闻其详

>Aaron: 如果这个交易代理，我是说如果
>Aaron: 真的能通过在模拟环境里学习
>Aaron: 掌握了一些超越我们人类直觉
>Aaron: 甚至是反直觉的、能赚钱的策略
>Aaron: 你会怎么看？

>Flora: 这个我懂。就是你常说的，AlphaGo在很多出招上面
>Flora: 推翻了人类几百年来的定式
>Flora: 这些都是人类直觉想都想不到的智慧

>Aaron: 正是这样
>Aaron: 如果强化学习能自我演化出交易智慧
>Aaron: 那么未来的量化人，我们的贡献又会是什么呢？

>Flora: 哇哦，这可是一大挑战！看起来挺可怕的

>Aaron: 对,这可能是另一个羊吃人的故事了
>Aaron: 不过，不管未来如何，人类真正的竞争对手
>Aaron: 从来都是人类自己

>Flora: 是的。正如AlphaGo天下无敌，但人类还在跟自己下围棋、争夺冠军

>Aaron: 是的，AlphaGo现在也只是作为人类的陪练。
>Aaron: 所以重要的，是我们自己先把强化学习学起来，用起来

>Flora: 是的。保持自己的相对竞争力，才是最重要的
>Flora: 好，今天的《量化好声音》就到这里
>Flora: 今天我们介绍了一个强化学习的量化交易模型
>Flora: 现在订阅匡醍 Research Platform，即可获得这个模型
>Flora: 我们下期再见。

>Aaron: 再见。
