# 21 天驯化 AI 打工仔：开发量化交易系统
## （一）实现 Redis 消息队列 和 从 Tushare 获取 OHLC 数据

<!--
!!! question
    本人和本人的 AI黑奴 的相互协作，能不能在短短 21 天内开发出一套量化交易系统？
-->


这么有意思的挑战，不如就从今天开始吧！“数据是一切开始的基础”，我打算先安排 AI黑奴 从数据获取开始做起。（感觉叫 AI黑奴 不太好听，那就给它取个名字叫：007号打码机，希望007号“牛码”可以“码力全开”）

好！下面我们正式准备开发工作！

![](1_00.png)

!!!info **Redis 启动！**

**Requirement**：我希望开发一个量化交易框架，并且使用 Redis 构建消息队列，从 Tushare 获取 OHLC 数据和复权因子。

**首先，考虑代码的结构**

需要分为两个主要部分：「数据获取生产者」和「消费者处理数据」

「生产者」从 Tushare 获取数据，然后将数据放入 Redis 队列；
「消费者」从队列中取出数据进行处理。需要处理 Tushare 的 API 调用，包括**获取 OHLC 和复权因子**，这里可能需要使用 tushare 的 **pro_api**，所以需要用户提供 token。

**接下来，Redis 部分，使用 「redis-py 库」**

「生产者」使用 lpush 将数据推入队列，「消费者」使用 brpop 阻塞式获取数据。需要注意异常处理，比如 Tushare 的 API 调用可能失败，Redis 连接问题等，所以需要添加 try-except 块。

**然后，考虑代码的独立性和可运行性**

  需要导入必要的库，如 redis、tushare、json 等。生产者函数中，获取数据后转换为 JSON 格式再推入队列。消费者函数中，取出数据后解析并打印，这里可以扩展为实际的处理逻辑，比如存储到数据库或进一步计算。

还要注意配置部分，将 Redis 的连接参数和 Tushare 的 token 作为配置项，方便用户修改。代码中使用注释说明需要用户替换的部分，比如 tushare_token 和 redis 的 host、port 等。

最后，确保 doubaocanvas 的格式正确，identifier 使用 “quant-data-redis-queue”，type 为 general_code，title 描述清楚。两个代码部分分别作为生产者和消费者的脚本，path 分别为 producer.py 和 consumer.py，description 准确。检查是否有未定义的变量，确保代码完整，可以独立运行，用户只需替换配置即可使用。

我的007表示这很简单，瞬间根据我的要求，给了两份代码文件：`data_producer.py` 和 `data_consumer.py`。

![](1_回答1.png)


```python
"""data_consumer.py"""
import redis
import json
from typing import Dict

# 配置参数（需与生产者保持一致）
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_QUEUE_NAME = "tushare_data_queue"

redis_client = redis.StrictRedis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)

def process_data(data_package: Dict):
    """数据处理逻辑（可扩展为实际业务处理）"""
    print(f"接收到数据时间: {data_package['timestamp']}")
    print(f"处理股票代码: {data_package['ts_code']}")
    print(f"OHLC数据条目数: {len(data_package['ohlc_data'])}")
    print(f"复权因子条目数: {len(data_package['adj_factor'])}")
    # 此处可添加数据存储/计算/分发等实际业务逻辑

def consume_data():
    """数据消费主函数（阻塞式获取）"""
    print("启动数据消费者，等待队列数据...")
    while True:
        try:
            # 阻塞式获取队列数据（超时时间30秒）
            _, json_data = redis_client.brpop(REDIS_QUEUE_NAME, timeout=30)
            data_package = json.loads(json_data)
            process_data(data_package)
            
        except Exception as e:
            print(f"数据处理异常: {str(e)}")
            continue

if __name__ == "__main__":
    consume_data()
```


```python
"""data_producer.py"""
import redis
import tushare as ts
import json
from datetime import datetime

# 配置参数
TUSHARE_TOKEN = "87a5a442931d15e749b4453b4f802c3b87794c3a5968b7b40a153edd"  # 替换为你的Tushare token
REDIS_HOST = "localhost"
REDIS_PORT = 6379
REDIS_QUEUE_NAME = "tushare_data_queue"

# 初始化连接
pro = ts.pro_api(TUSHARE_TOKEN)
redis_client = redis.StrictRedis(host=REDIS_HOST, port=REDIS_PORT, decode_responses=True)

def fetch_ohlc_data(ts_code: str, start_date: str, end_date: str) -> list:
    """获取OHLC数据"""
    try:
        df = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
        return df[['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol']].to_dict('records')
    except Exception as e:
        print(f"OHLC数据获取失败: {str(e)}")
        return []

def fetch_adj_factor(ts_code: str, start_date: str, end_date: str) -> list:
    """获取复权因子"""
    try:
        df = pro.adj_factor(ts_code=ts_code, start_date=start_date, end_date=end_date)
        return df[['ts_code', 'trade_date', 'adj_factor']].to_dict('records')
    except Exception as e:
        print(f"复权因子获取失败: {str(e)}")
        return []

def produce_data(ts_code_list: list, date_range: tuple):
    """数据生产主函数"""
    start_date, end_date = date_range
    for ts_code in ts_code_list:
        # 封装数据结构
        data_package = {
            "timestamp": datetime.now().isoformat(),
            "ts_code": ts_code,
            "ohlc_data": fetch_ohlc_data(ts_code, start_date, end_date),
            "adj_factor": fetch_adj_factor(ts_code, start_date, end_date)
        }
        
        # 推入Redis队列
        redis_client.lpush(REDIS_QUEUE_NAME, json.dumps(data_package))
        print(f"已推送数据: {ts_code} - {start_date}至{end_date}")

if __name__ == "__main__":
    # 示例参数
    STOCK_CODES = ["000001.SZ", "600519.SH"]  # 沪深300成分股示例
    DATE_RANGE = ("20230101", "20231231")   # 年度数据范围
    
    produce_data(STOCK_CODES, DATE_RANGE)
```



运行了两个文件没有反应，原来是这个电脑还没有下载并启动 Redis 。然后，我快速手动下载并启动，并夸赞了 007 贴心给我写了一段检查 Redis 是否正常启动的代码。

<!--
!!! note
    **mac 安装并启动 Redis**
    - mac 安装 Redis ，国内 npm 会快一些：
    `/bin/zsh -c "$(curl -fsSL https://gitee.com/cunkai/HomebrewCN/raw/master/Homebrew.sh)"`
    - 使用 Homebrew 安装命令：
    `brew install redis`
    - 启动 redis 服务：
    `brew services start redis`
    `redis-server`
-->

![](1_01.png)


此时，运行 007 的“贴心代码”，就显示成功连接到 Redis：

![](1_02.png)


为了直观地查看和管理 Redis 中的数据，我安装了 [RedisInsight](https://redis.io/insight/)

## Tushare 获取数据

完成了 Redis 的连接，直接来运行 007 提供的 `data_producer.py`，成功在 RedisInsight 中看到了数据：

![](1_03.png)


再运行 `data_consumer.py`，成功从消息队列中取出数据。007 还为我讲解了一下 producer 和 consumer 之间的关系，今天又是依赖 AI 打工仔的一天呢～

<!--
!!! note
    **生产者（producer）和消费者（consumer）之间的关系**
    - 生产者（producer）：负责从数据源（这里是 Tushare）获取数据，对数据进行必要的处理和封装，然后将数据推送到 Redis 消息队列中。在这个过程中，生产者不关心谁会使用这些数据，它只需要将数据放入队列即可。
    - 消费者（consumer）：从 Redis 消息队列中取出数据，对数据进行处理。消费者可以有多个，它们可以并行地从队列中获取数据，提高数据处理的效率。消费者不关心数据是从哪里来的，只需要从队列中获取数据并进行处理。
    - 解耦：生产者和消费者通过 Redis 消息队列进行通信，它们之间是松耦合的关系。这意味着生产者和消费者可以独立开发、部署和扩展，互不影响。例如，如果需要增加一个新的数据源，只需要修改生产者代码；如果需要增加数据处理的逻辑，只需要修改消费者代码。
    - 异步处理：生产者和消费者的操作是异步的。生产者将数据放入队列后，不需要等待消费者处理完数据就可以继续获取和推送新的数据。消费者可以根据自己的处理能力从队列中获取数据，避免了生产者和消费者之间的同步等待，提高了系统的整体性能。
    - 数据缓冲：Redis 消息队列起到了数据缓冲的作用。当生产者产生数据的速度大于消费者处理数据的速度时，队列可以暂时存储数据，避免数据丢失。当消费者处理数据的速度大于生产者产生数据的速度时，队列可以提供数据，保证消费者不会因为没有数据而闲置。
-->

![](1_05.png)

## 总结
!!! 通过 Redis 消息队列的设计，我和 007 实现了生产者和消费者的解耦与异步处理，极大地提升了系统的灵活性和性能。生产者专注于从 Tushare 获取数据，消费者则负责数据的处理和扩展。

!!! 本次开发不仅验证了 Redis 消息队列在量化交易系统中的高效性，也让我对 007 的能力刮目相看。接下来，我将继续优化数据处理逻辑，为量化交易系统的核心算法打下坚实基础。

**21 天的挑战才刚刚开始，期待更多精彩！**