This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=latex 2025.6.16)  8 JUL 2025 11:34
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**media/Tex/27c65bd3a5709631.tex
(./media/Tex/27c65bd3a5709631.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(/usr/local/texlive/2025/texmf-dist/tex/latex/standalone/standalone.cls
Document Class: standalone 2025/02/22 v1.5a Class to compile TeX sub-files stan
dalone
(/usr/local/texlive/2025/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2023/07/08 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
) (/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
(/usr/local/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
(/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/usr/local
/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks17
\XKV@tempa@toks=\toks18
(/usr/local/texlive/2025/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count196
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\sa@internal=\count197
\c@sapage=\count198
(/usr/local/texlive/2025/texmf-dist/tex/latex/standalone/standalone.cfg
File: standalone.cfg 2025/02/22 v1.5a Default configuration file for 'standalon
e' class
) (/usr/local/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(/usr/local/texlive/2025/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count199
\c@section=\count266
\c@subsection=\count267
\c@subsubsection=\count268
\c@paragraph=\count269
\c@subparagraph=\count270
\c@figure=\count271
\c@table=\count272
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (/usr/local/texlive/2025/texmf-dist/tex/latex/preview/preview.sty
Package: preview 2024/06/30 14.0.6 (AUCTeX/preview-latex)
(/usr/local/texlive/2025/texmf-dist/tex/generic/luatex85/luatex85.sty
Package: luatex85 2016/06/15 v1.4 pdftex aliases for luatex
) (/usr/local/texlive/2025/texmf-dist/tex/latex/preview/prtightpage.def
\PreviewBorder=\dimen142
)
\pr@snippet=\count273
\pr@box=\box52
\pr@output=\toks19
)) (/usr/local/texlive/2025/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/02/14 v25.4 The multilingual framework for pdfLaTeX, LuaLaT
eX and XeLaTeX
\babel@savecnt=\count274
\U@D=\dimen143
\l@unhyphenated=\language90
(/usr/local/texlive/2025/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count275
(/usr/local/texlive/2025/texmf-dist/tex/generic/babel-english/english.ldf
Language: english 2017/06/06 v3.3r English support from the babel system
Package babel Info: Hyphen rules for 'canadian' set to \l@english
(babel)             (\language0). Reported on input line 102.
Package babel Info: Hyphen rules for 'australian' set to \l@ukenglish
(babel)             (\language22). Reported on input line 105.
Package babel Info: Hyphen rules for 'newzealand' set to \l@ukenglish
(babel)             (\language22). Reported on input line 108.
))
(/usr/local/texlive/2025/texmf-dist/tex/generic/babel/locale/en/babel-english.t
ex
Package babel Info: Importing font and identification data for english
(babel)             from babel-en.ini. Reported on input line 11.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen144
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen145
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count276
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count277
\leftroot@=\count278
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count279
\DOTSCASE@=\count280
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen146
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count281
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count282
\dotsspace@=\muskip17
\c@parentequation=\count283
\dspbrk@lvl=\count284
\tag@help=\toks21
\row@=\count285
\column@=\count286
\maxfields@=\count287
\andhelp@=\toks22
\eqnshift@=\dimen147
\alignsep@=\dimen148
\tagshift@=\dimen149
\tagwidth@=\dimen150
\totwidth@=\dimen151
\lineht@=\dimen152
\@envbody=\toks23
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.def
File: l3backend-dvips.def 2024-05-08 L3 backend support: dvips
\l__pdf_internal_box=\box55
\l__pdf_backend_content_box=\box56
\l__pdf_backend_model_box=\box57
\g__pdf_backend_annotation_int=\count288
\g__pdf_backend_link_int=\count289
\g__pdf_backend_link_sf_int=\count290
)
No file 27c65bd3a5709631.aux.
\openout1 = `27c65bd3a5709631.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 5.
LaTeX Font Info:    ... okay on input line 5.
Preview: Fontsize 10pt
LaTeX Font Info:    Trying to load font information for U+msa on input line 6.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 6.
(/usr/local/texlive/2025/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

! LaTeX Error: Unicode character 的 (U+7684)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.8 \end{align*}
                
You may provide a definition with
\DeclareUnicodeCharacter 

 
Here is how much of TeX's memory you used:
 3496 strings out of 473191
 57652 string characters out of 5715826
 453853 words of memory out of 5000000
 26742 multiletter control sequences out of 15000+600000
 560536 words of font info for 44 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 70i,8n,71p,242b,166s stack positions out of 10000i,1000n,20000p,200000b,200000s
No pages of output.
