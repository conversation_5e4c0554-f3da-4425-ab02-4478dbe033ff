<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>匡醒(Quantide)好课推荐 - 筑基新手套餐</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .course-card {
            background: #f8f9fa;
            border-radius: 30px;
            padding: 40px 30px;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .course-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            border-radius: 30px 30px 0 0;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }

        .header h1 {
            font-size: 20px;
            font-weight: 500;
            margin-top: 20px;
        }

        .main-title {
            text-align: center;
            color: #4a90a4;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 40px;
            letter-spacing: 2px;
        }

        .features {
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            animation: fadeInUp 0.6s ease-out;
        }

        .feature-item:nth-child(1) { animation-delay: 0.1s; }
        .feature-item:nth-child(2) { animation-delay: 0.2s; }
        .feature-item:nth-child(3) { animation-delay: 0.3s; }
        .feature-item:nth-child(4) { animation-delay: 0.4s; }

        .feature-number {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .feature-text {
            background: white;
            padding: 12px 20px;
            border-radius: 25px;
            border: 2px solid #e0e6ed;
            color: #4a90a4;
            font-size: 16px;
            font-weight: 500;
            flex: 1;
        }

        .bottom-section {
            background: linear-gradient(135deg, #4a90a4 0%, #357a8a 100%);
            margin: 0 -30px -40px -30px;
            padding: 30px;
            border-radius: 0 0 30px 30px;
            color: white;
            position: relative;
        }

        .price-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-bottom: 20px;
        }

        .price-label {
            font-size: 18px;
            margin-bottom: 5px;
        }

        .price {
            font-size: 48px;
            font-weight: bold;
            line-height: 1;
        }

        .limited-time {
            text-align: right;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            margin-bottom: 10px;
        }

        .description {
            background: rgba(255, 193, 7, 0.9);
            color: #333;
            padding: 15px 20px;
            border-radius: 15px;
            font-size: 14px;
            text-align: center;
            line-height: 1.5;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .course-card {
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .course-card {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .main-title {
                font-size: 28px;
            }
            
            .price {
                font-size: 36px;
            }
            
            .feature-text {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="course-card">
        <div class="header">
            <h1>匡醒 (Quantide) 好课推荐</h1>
        </div>
        
        <h2 class="main-title">筑基新手套餐</h2>
        
        <div class="features">
            <div class="feature-item">
                <div class="feature-number">1</div>
                <div class="feature-text">新手上路 大群答疑 适合自学</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-number">2</div>
                <div class="feature-text">包含课件、代码、数据、视频</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-number">3</div>
                <div class="feature-text">共享云端环境 课件代码可在线运行</div>
            </div>
            
            <div class="feature-item">
                <div class="feature-number">4</div>
                <div class="feature-text">可随时升级到高阶课程 原价抵扣新课学费</div>
            </div>
        </div>
        
        <div class="bottom-section">
            <div class="price-section">
                <div>
                    <div class="price-label">筑基新手</div>
                    <div class="price">1699</div>
                </div>
                <div>
                    <div class="limited-time">限时秒杀 限时下架</div>
                </div>
            </div>
            
            <div class="description">
                套餐包含文本、代码、数据、视频和大群答疑<br>
                不含第19/20课预测模型
            </div>
        </div>
    </div>
</body>
</html>
