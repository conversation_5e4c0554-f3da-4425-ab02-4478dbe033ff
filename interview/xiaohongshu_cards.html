<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化面试题小红书卡片</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            width: 450px;
            min-height: 600px;
            height: auto;
            margin: 30px;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: visible;
            display: inline-block;
            vertical-align: top;
            clear: both;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            pointer-events: none;
        }
        
        /* 主题卡片样式 - 多种渐变色 */
        .card-1 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%); }
        .card-2 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .card-3 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .card-4 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
        .card-5 { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }
        .card-6 { background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); }
        .card-7 { background: linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%); }
        .card-8 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .card-9 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .card-10 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }

        .card {
            color: #2d3748;
        }
        
        .card-header {
            text-align: center;
            margin-bottom: 25px;
        }
        
        .card-number {
            background: rgba(255,255,255,0.9);
            color: #667eea;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 26px;
            font-weight: bold;
            line-height: 1.3;
            margin: 0;
        }

        .card-content {
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        .highlight {
            background: rgba(255,255,255,0.8);
            padding: 15px;
            border-radius: 12px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .formula {
            background: rgba(255,255,255,0.9);
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            text-align: center;
            margin: 10px 0;
            font-weight: bold;
            color: #e53e3e;
        }
        
        .key-points {
            background: rgba(255,255,255,0.7);
            padding: 15px;
            border-radius: 12px;
            margin: 15px 0;
        }
        
        .key-points ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .key-points li {
            margin: 8px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .tag {
            background: rgba(255,255,255,0.8);
            color: #667eea;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
            display: inline-block;
        }
        
        .footer {
            position: absolute;
            bottom: 20px;
            left: 30px;
            right: 30px;
            text-align: center;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .emoji {
            font-size: 20px;
            margin-right: 8px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .card {
                width: 380px;
                min-height: 500px;
                padding: 30px;
                margin: 20px auto;
                display: block;
            }

            .card-title {
                font-size: 22px;
            }

            .card-content {
                font-size: 16px;
            }
        }

        /* 确保卡片不重叠 */
        .container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- 题目1：回归系数乘积范围 -->
        <div class="card card-1">
            <div class="card-header">
                <div class="card-number">📊 面试题 01</div>
                <h2 class="card-title">回归系数乘积的范围</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>对Y关于X回归得β₁，对X关于Y回归得β₂，求β₁×β₂的范围？</p>
                
                <div class="highlight">
                    <div class="formula">β₁ × β₂ = ρ²</div>
                    <p style="text-align: center; margin: 5px 0;"><strong>范围：[0, 1]</strong></p>
                </div>
                
                <div class="key-points">
                    <strong>💡 核心要点：</strong>
                    <ul>
                        <li>等于相关系数的平方</li>
                        <li>反映线性关系强度</li>
                        <li>完全相关时 = 1</li>
                        <li>无相关时 = 0</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#量化面试</span>
                    <span class="tag">#回归分析</span>
                    <span class="tag">#统计学</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">📈</span>量化研究必备知识点
            </div>
        </div>

        <!-- 题目2：重复数据影响 -->
        <div class="card card-2">
            <div class="card-header">
                <div class="card-number">📊 面试题 02</div>
                <h2 class="card-title">重复数据对回归的影响</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>将X和Y数据重复一遍，如何影响β、R²和p值？</p>
                
                <div class="key-points">
                    <strong>📋 影响结果：</strong>
                    <ul>
                        <li><strong>β系数：</strong>不变 ✅</li>
                        <li><strong>R²：</strong>不变 ✅</li>
                        <li><strong>p值：</strong>显著降低 ⚠️</li>
                        <li><strong>标准误差：</strong>减小1/√2</li>
                    </ul>
                </div>
                
                <div class="highlight">
                    <strong>⚠️ 关键警示：</strong><br>
                    重复数据会产生虚假的统计显著性！这是数据预处理中需要特别注意的陷阱。
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#数据清洗</span>
                    <span class="tag">#统计陷阱</span>
                    <span class="tag">#回归分析</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">⚠️</span>数据质量是关键
            </div>
        </div>

        <!-- 题目3：R²符号性质 -->
        <div class="card card-3">
            <div class="card-header">
                <div class="card-number">📊 面试题 03</div>
                <h2 class="card-title">R²的符号性质</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>线性回归中R²总是正数吗？无截距项会怎样？</p>
                
                <div class="key-points">
                    <strong>📋 答案总结：</strong>
                    <ul>
                        <li><strong>有截距：</strong>R² ∈ [0, 1] ✅</li>
                        <li><strong>无截距：</strong>R² ∈ (-∞, 1] ⚠️</li>
                    </ul>
                </div>
                
                <div class="highlight">
                    <strong>💡 关键洞察：</strong><br>
                    无截距回归的负R²表明模型比简单均值模型表现更差，说明模型设定不当！
                </div>
                
                <div class="formula">
                    R² = 1 - SSE/SST
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#模型诊断</span>
                    <span class="tag">#回归分析</span>
                    <span class="tag">#统计学</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">📐</span>模型设定很重要
            </div>
        </div>
        
        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 03</div>
                <h2 class="card-title">R²符号验证</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score

# 生成不通过原点的数据
np.random.seed(42)
x = np.array([1, 2, 3, 4, 5]).reshape(-1, 1)
y = 10 + 2*x.flatten() + np.random.normal(0, 0.1, 5)

# 有截距回归
model_with = LinearRegression(fit_intercept=True)
model_with.fit(x, y)
y_pred_with = model_with.predict(x)
r2_with = r2_score(y, y_pred_with)

# 无截距回归
model_no = LinearRegression(fit_intercept=False)
model_no.fit(x, y)
y_pred_no = model_no.predict(x)

# 计算无截距R²
sse_no = np.sum((y - y_pred_no)**2)
sst = np.sum((y - np.mean(y))**2)
r2_no = 1 - sse_no/sst

print(f"有截距R²: {r2_with:.3f}")
print(f"无截距R²: {r2_no:.3f}")
print(f"无截距可能为负: {r2_no < 0}")
                </div>
                
                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#sklearn</span>
                    <span class="tag">#模型对比</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🧪</span>实验验证理论
            </div>
        </div>


        <!-- 题目4：多元回归R²范围 -->
        <div class="card main-card">
            <div class="card-header">
                <div class="card-number">📊 面试题 04</div>
                <h2 class="card-title">多元回归R²的范围</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Y~X1得R²₁=0.3，Y~X2得R²₂=0.4，Y~X1+X2的R²范围？</p>

                <div class="highlight">
                    <div class="formula">范围：[0.4, 0.7]</div>
                </div>

                <div class="key-points">
                    <strong>📋 范围分析：</strong>
                    <ul>
                        <li><strong>下界：</strong>max(R²₁, R²₂) = 0.4</li>
                        <li><strong>上界：</strong>X₁、X₂无相关时 = 0.7</li>
                        <li><strong>影响因素：</strong>变量间相关性</li>
                    </ul>
                </div>

                <div class="highlight">
                    <strong>💡 关键洞察：</strong><br>
                    变量间相关性越高，多元回归的改善越小！
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#多元回归</span>
                    <span class="tag">#变量选择</span>
                    <span class="tag">#多重共线性</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🔗</span>变量关系是关键
            </div>
        </div>

        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 04</div>
                <h2 class="card-title">多元回归R²验证</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score

# 模拟数据
np.random.seed(42)
n = 1000

# 生成相关变量
X1 = np.random.normal(0, 1, n)
X2 = np.random.normal(0, 1, n)
y = 0.5*X1 + 0.6*X2 + np.random.normal(0, 1, n)

# 单变量回归
model1 = LinearRegression()
model1.fit(X1.reshape(-1, 1), y)
r2_1 = model1.score(X1.reshape(-1, 1), y)

model2 = LinearRegression()
model2.fit(X2.reshape(-1, 1), y)
r2_2 = model2.score(X2.reshape(-1, 1), y)

# 多元回归
X_multi = np.column_stack([X1, X2])
model_multi = LinearRegression()
model_multi.fit(X_multi, y)
r2_multi = model_multi.score(X_multi, y)

print(f"R²₁: {r2_1:.3f}")
print(f"R²₂: {r2_2:.3f}")
print(f"多元R²: {r2_multi:.3f}")
print(f"理论范围: [{max(r2_1, r2_2):.3f}, {r2_1+r2_2:.3f}]")
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#多元分析</span>
                    <span class="tag">#模型比较</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">📊</span>数据说话
            </div>
        </div>

        <!-- 题目5：误差偏差分析 -->
        <div class="card main-card">
            <div class="card-header">
                <div class="card-number">📊 面试题 05</div>
                <h2 class="card-title">误差项对模型偏差的影响</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Y=WX+b+e 和 Y=W(X+e)+b 中，e如何影响偏差？</p>

                <div class="key-points">
                    <strong>📋 影响对比：</strong>
                    <ul>
                        <li><strong>因变量误差：</strong>无偏估计 ✅</li>
                        <li><strong>自变量误差：</strong>衰减偏差 ⚠️</li>
                    </ul>
                </div>

                <div class="highlight">
                    <div class="formula">衰减因子：λ = Var(X) / [Var(X) + Var(e)]</div>
                </div>

                <div class="highlight">
                    <strong>💡 关键洞察：</strong><br>
                    测量误差会导致系数向零偏移，这是计量经济学中的重要问题！
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#测量误差</span>
                    <span class="tag">#计量经济学</span>
                    <span class="tag">#偏差分析</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🎯</span>测量精度很重要
            </div>
        </div>

        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 05</div>
                <h2 class="card-title">误差偏差验证</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
from sklearn.linear_model import LinearRegression

np.random.seed(42)
n = 1000
W_true = 2.0
b_true = 1.0
sigma_e = 1.0

# 生成真实数据
X_true = np.random.normal(0, 2, n)
Y_true = W_true * X_true + b_true

# 情况1：Y = WX + b + e (因变量误差)
e1 = np.random.normal(0, sigma_e, n)
Y1 = Y_true + e1
model1 = LinearRegression()
model1.fit(X_true.reshape(-1, 1), Y1)

# 情况2：Y = W(X + e) + b (自变量误差)
e2 = np.random.normal(0, sigma_e, n)
X_observed = X_true + e2
Y2 = W_true * X_observed + b_true
model2 = LinearRegression()
model2.fit(X_observed.reshape(-1, 1), Y2)

# 理论衰减因子
var_X = np.var(X_true)
var_e = sigma_e**2
attenuation = var_X / (var_X + var_e)

print(f"真实系数: {W_true}")
print(f"情况1估计: {model1.coef_[0]:.4f} (偏差: {model1.coef_[0] - W_true:.4f})")
print(f"情况2估计: {model2.coef_[0]:.4f} (偏差: {model2.coef_[0] - W_true:.4f})")
print(f"理论衰减: {W_true * attenuation:.4f}")
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#偏差检验</span>
                    <span class="tag">#蒙特卡洛</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🔬</span>理论与实践结合
            </div>
        </div>


        <!-- 题目6：缺失数据处理 -->
        <div class="card main-card">
            <div class="card-header">
                <div class="card-number">📊 面试题 06</div>
                <h2 class="card-title">缺失数据处理方法</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>如何处理缺失数据？如何进行插补？</p>

                <div class="key-points">
                    <strong>📋 缺失机制：</strong>
                    <ul>
                        <li><strong>MCAR：</strong>完全随机缺失</li>
                        <li><strong>MAR：</strong>随机缺失</li>
                        <li><strong>MNAR：</strong>非随机缺失</li>
                    </ul>
                </div>

                <div class="key-points">
                    <strong>🛠️ 处理方法：</strong>
                    <ul>
                        <li>删除法（列表/成对删除）</li>
                        <li>简单插补（均值/中位数/众数）</li>
                        <li>高级插补（MICE/KNN/回归）</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#数据预处理</span>
                    <span class="tag">#缺失值</span>
                    <span class="tag">#数据清洗</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🔧</span>数据预处理是基础
            </div>
        </div>

        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 06</div>
                <h2 class="card-title">缺失数据处理</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
import pandas as pd
from sklearn.impute import SimpleImputer, KNNImputer
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer

# 创建含缺失值的数据
np.random.seed(42)
data = np.random.randn(100, 4)
missing_mask = np.random.random((100, 4)) < 0.1
data[missing_mask] = np.nan

df = pd.DataFrame(data, columns=['A', 'B', 'C', 'D'])

print(f"缺失值统计:\n{df.isnull().sum()}")

# 方法1：简单插补
simple_imputer = SimpleImputer(strategy='mean')
df_simple = pd.DataFrame(simple_imputer.fit_transform(df),
                        columns=df.columns)

# 方法2：KNN插补
knn_imputer = KNNImputer(n_neighbors=5)
df_knn = pd.DataFrame(knn_imputer.fit_transform(df),
                     columns=df.columns)

# 方法3：MICE插补
mice_imputer = IterativeImputer(random_state=42)
df_mice = pd.DataFrame(mice_imputer.fit_transform(df),
                      columns=df.columns)

print("插补完成！")
print(f"简单插补后缺失值: {df_simple.isnull().sum().sum()}")
print(f"KNN插补后缺失值: {df_knn.isnull().sum().sum()}")
print(f"MICE插补后缺失值: {df_mice.isnull().sum().sum()}")
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#pandas</span>
                    <span class="tag">#sklearn</span>
                    <span class="tag">#数据插补</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🛠️</span>工具箱很重要
            </div>
        </div>

        <!-- 题目7：密度拟合策略 -->
        <div class="card main-card">
            <div class="card-header">
                <div class="card-number">📊 面试题 07</div>
                <h2 class="card-title">不同密度区域拟合策略</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>不同区域具有不同密度的随机点，最佳拟合策略？</p>

                <div class="key-points">
                    <strong>🎯 策略选择：</strong>
                    <ul>
                        <li><strong>加权拟合：</strong>根据密度分配权重</li>
                        <li><strong>局部拟合：</strong>LOESS、局部多项式</li>
                        <li><strong>分段拟合：</strong>自适应分段</li>
                        <li><strong>多尺度：</strong>小波、高斯过程</li>
                    </ul>
                </div>

                <div class="highlight">
                    <strong>💡 选择原则：</strong><br>
                    根据密度变化程度、数据量和计算资源选择最适合的策略
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#非参数回归</span>
                    <span class="tag">#局部拟合</span>
                    <span class="tag">#自适应方法</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🎨</span>因地制宜很重要
            </div>
        </div>

        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 07</div>
                <h2 class="card-title">密度拟合实现</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
from sklearn.neighbors import KernelDensity
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
import matplotlib.pyplot as plt

def estimate_density(X, bandwidth=1.0):
    """估计局部密度"""
    kde = KernelDensity(bandwidth=bandwidth)
    kde.fit(X.reshape(-1, 1))
    log_density = kde.score_samples(X.reshape(-1, 1))
    return np.exp(log_density)

def weighted_polynomial_fit(X, y, weights, degree=2):
    """加权多项式拟合"""
    poly_features = PolynomialFeatures(degree=degree)
    X_poly = poly_features.fit_transform(X.reshape(-1, 1))

    model = LinearRegression()
    model.fit(X_poly, y, sample_weight=weights)

    return model, poly_features

# 生成不同密度的数据
np.random.seed(42)
X1 = np.random.normal(2, 0.5, 100)  # 高密度区域
X2 = np.random.normal(8, 1, 20)     # 低密度区域
X = np.concatenate([X1, X2])
y = X**2 + np.random.normal(0, 0.5, len(X))

# 估计密度并分配权重
density = estimate_density(X)
weights = density / np.max(density)

# 加权拟合
model, poly_features = weighted_polynomial_fit(X, y, weights)

print("密度加权拟合完成！")
print(f"数据点数: {len(X)}")
print(f"权重范围: [{weights.min():.3f}, {weights.max():.3f}]")
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#密度估计</span>
                    <span class="tag">#加权回归</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">⚖️</span>权重分配是艺术
            </div>
        </div>


        <!-- 题目8：Lasso vs Ridge -->
        <div class="card main-card">
            <div class="card-header">
                <div class="card-number">📊 面试题 08</div>
                <h2 class="card-title">Lasso与Ridge回归对比</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Lasso与Ridge的区别</p>

                <div class="key-points">
                    <strong>🔍 核心差异：</strong>
                    <ul>
                        <li><strong>Ridge (L2)：</strong>连续收缩，处理共线性</li>
                        <li><strong>Lasso (L1)：</strong>稀疏化，自动特征选择</li>
                        <li><strong>Elastic Net：</strong>结合两者优点</li>
                    </ul>
                </div>

                <div class="highlight">
                    <div class="formula">Ridge: ||β||²₂ vs Lasso: ||β||₁</div>
                </div>

                <div class="highlight">
                    <strong>💡 选择指南：</strong><br>
                    预测为主选Ridge，特征选择选Lasso，兼顾选Elastic Net
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#正则化</span>
                    <span class="tag">#特征选择</span>
                    <span class="tag">#机器学习</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">⚖️</span>正则化是平衡艺术
            </div>
        </div>

        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 08</div>
                <h2 class="card-title">Lasso vs Ridge对比</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.datasets import make_regression
from sklearn.model_selection import train_test_split

# 生成稀疏数据
X, y = make_regression(n_samples=100, n_features=20,
                      n_informative=5, noise=0.1, random_state=42)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3)

# 模型对比
models = {
    'Ridge': Ridge(alpha=1.0),
    'Lasso': Lasso(alpha=0.1),
    'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5)
}

results = {}
for name, model in models.items():
    model.fit(X_train, y_train)
    score = model.score(X_test, y_test)
    n_features = np.sum(np.abs(model.coef_) > 0.01)

    results[name] = {
        'score': score,
        'features': n_features,
        'coef_norm': np.linalg.norm(model.coef_)
    }

print("模型对比结果:")
for name, result in results.items():
    print(f"{name}:")
    print(f"  R²: {result['score']:.3f}")
    print(f"  选择特征数: {result['features']}")
    print(f"  系数范数: {result['coef_norm']:.3f}")
    print()

print("特征选择效果:")
print(f"原始特征数: {X.shape[1]}")
print(f"Lasso选择: {results['Lasso']['features']}")
print(f"Ridge选择: {results['Ridge']['features']}")
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#sklearn</span>
                    <span class="tag">#模型对比</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🎯</span>实践出真知
            </div>
        </div>

        <!-- 题目9：集成学习对比 -->
        <div class="card main-card">
            <div class="card-header">
                <div class="card-number">📊 面试题 09</div>
                <h2 class="card-title">集成学习方法对比</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Bagging与Boosting的区别，XGBoost与GBM的区别</p>

                <div class="key-points">
                    <strong>🔄 Bagging vs Boosting：</strong>
                    <ul>
                        <li><strong>Bagging：</strong>并行训练，降方差</li>
                        <li><strong>Boosting：</strong>串行训练，降偏差</li>
                    </ul>
                </div>

                <div class="key-points">
                    <strong>🚀 XGBoost vs GBM：</strong>
                    <ul>
                        <li><strong>GBM：</strong>一阶梯度，基础正则化</li>
                        <li><strong>XGBoost：</strong>二阶优化，工程优化</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#集成学习</span>
                    <span class="tag">#随机森林</span>
                    <span class="tag">#梯度提升</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🤝</span>团结就是力量
            </div>
        </div>

        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 09</div>
                <h2 class="card-title">集成学习对比</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
from sklearn.ensemble import RandomForestClassifier, AdaBoostClassifier, GradientBoostingClassifier
from sklearn.datasets import make_classification
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import xgboost as xgb

# 生成分类数据
X, y = make_classification(n_samples=1000, n_features=20,
                          n_informative=10, random_state=42)
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3)

# 模型对比
models = {
    'Random Forest (Bagging)': RandomForestClassifier(n_estimators=100, random_state=42),
    'AdaBoost (Boosting)': AdaBoostClassifier(n_estimators=100, random_state=42),
    'GBM': GradientBoostingClassifier(n_estimators=100, random_state=42),
    'XGBoost': xgb.XGBClassifier(n_estimators=100, random_state=42, eval_metric='logloss')
}

print("集成学习方法对比:")
print("-" * 40)

for name, model in models.items():
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    print(f"{name}:")
    print(f"  准确率: {accuracy:.4f}")

    # 特征重要性（如果有的话）
    if hasattr(model, 'feature_importances_'):
        top_features = np.argsort(model.feature_importances_)[-3:]
        print(f"  重要特征: {top_features}")
    print()
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#ensemble</span>
                    <span class="tag">#xgboost</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🏆</span>集成的力量
            </div>
        </div>


        <!-- 题目10：平稳性类型 -->
        <div class="card main-card">
            <div class="card-header">
                <div class="card-number">📊 面试题 10</div>
                <h2 class="card-title">弱平稳性与强平稳性</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>弱平稳性与强平稳性的区别</p>

                <div class="key-points">
                    <strong>📋 定义对比：</strong>
                    <ul>
                        <li><strong>强平稳：</strong>所有阶矩和分布不变</li>
                        <li><strong>弱平稳：</strong>仅前两阶矩不变</li>
                    </ul>
                </div>

                <div class="highlight">
                    <div class="formula">强平稳 ⟹ 弱平稳</div>
                    <p style="text-align: center; margin: 5px 0;">反之不成立</p>
                </div>

                <div class="highlight">
                    <strong>💡 实际应用：</strong><br>
                    弱平稳性是时间序列建模（ARIMA）的基础假设
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <span class="tag">#时间序列</span>
                    <span class="tag">#平稳性</span>
                    <span class="tag">#ARIMA</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">📈</span>时间序列的基石
            </div>
        </div>

        <div class="card code-card">
            <div class="card-header">
                <div class="card-number">💻 代码实现 10</div>
                <h2 class="card-title">平稳性检验</h2>
            </div>
            <div class="card-content">
                <div class="code-block">import numpy as np
import pandas as pd
from statsmodels.tsa.stattools import adfuller, kpss
import matplotlib.pyplot as plt

def adf_test(series, title="序列"):
    """ADF平稳性检验"""
    result = adfuller(series, autolag='AIC')

    print(f'{title} ADF检验:')
    print(f'  ADF统计量: {result[0]:.6f}')
    print(f'  p值: {result[1]:.6f}')

    if result[1] <= 0.05:
        print("  结论: 序列是平稳的 ✅")
    else:
        print("  结论: 序列可能非平稳 ⚠️")
    print()

# 生成不同类型的时间序列
np.random.seed(42)
n = 1000

# 1. 白噪声（强平稳）
white_noise = np.random.normal(0, 1, n)

# 2. AR(1)平稳过程（弱平稳）
ar1_stationary = np.zeros(n)
ar1_stationary[0] = 0
for t in range(1, n):
    ar1_stationary[t] = 0.7 * ar1_stationary[t-1] + np.random.normal(0, 1)

# 3. 随机游走（非平稳）
random_walk = np.cumsum(np.random.normal(0, 1, n))

# 4. 含趋势序列（非平稳）
trend_series = 0.01 * np.arange(n) + np.random.normal(0, 1, n)

# 平稳性检验
print("平稳性检验结果:")
print("=" * 40)
adf_test(white_noise, "白噪声")
adf_test(ar1_stationary, "AR(1)平稳")
adf_test(random_walk, "随机游走")
adf_test(trend_series, "含趋势序列")

# 一阶差分后的随机游走
diff_random_walk = np.diff(random_walk)
adf_test(diff_random_walk, "差分后随机游走")
                </div>

                <div style="text-align: center; margin-top: 15px;">
                    <span class="tag">#statsmodels</span>
                    <span class="tag">#ADF检验</span>
                    <span class="tag">#时间序列分析</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🔍</span>检验是第一步
            </div>
        </div>

        <!-- 总结卡片 -->
        <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; height: 600px;">
            <div class="card-header">
                <div class="card-number" style="background: rgba(255,255,255,0.2); color: white;">🎓 学习总结</div>
                <h2 class="card-title">量化面试题精华</h2>
            </div>
            <div class="card-content">
                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px; margin: 15px 0;">
                    <strong>🎯 核心知识点：</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>回归分析基础理论</li>
                        <li>统计检验与假设检验</li>
                        <li>机器学习算法对比</li>
                        <li>时间序列分析方法</li>
                        <li>数据预处理技巧</li>
                    </ul>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px; margin: 15px 0;">
                    <strong>💡 学习建议：</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>理论与实践相结合</li>
                        <li>多做代码练习</li>
                        <li>关注实际应用场景</li>
                        <li>掌握常用Python库</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <span class="tag" style="background: rgba(255,255,255,0.2); color: white;">#量化金融</span>
                    <span class="tag" style="background: rgba(255,255,255,0.2); color: white;">#数据科学</span>
                    <span class="tag" style="background: rgba(255,255,255,0.2); color: white;">#机器学习</span>
                </div>

                <div style="text-align: center; margin-top: 40px; font-size: 18px;">
                    <strong>🚀 加油！你一定可以的！</strong>
                </div>
            </div>
            <div class="footer" style="color: rgba(255,255,255,0.8);">
                <span class="emoji">🎉</span>持续学习，不断进步
            </div>
        </div>

    </div>
</body>
</html>
