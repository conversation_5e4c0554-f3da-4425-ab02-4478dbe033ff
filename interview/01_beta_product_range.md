# 面试题1：回归系数乘积的范围

## 题目
Regress Y on X get beta1, regress X on Y get beta 2. What is the range of beta1*beta2?
对Y关于X进行回归得到系数beta1，对X关于Y进行回归得到系数beta2。请问beta1*beta2的取值范围是什么？

## 解答

### 数学推导

设我们有两个回归方程：
1. Y = α₁ + β₁X + ε₁  (Y对X回归)
2. X = α₂ + β₂Y + ε₂  (X对Y回归)

其中：
- β₁ = Cov(X,Y) / Var(X)
- β₂ = Cov(X,Y) / Var(Y)

因此：
β₁ × β₂ = [Cov(X,Y) / Var(X)] × [Cov(X,Y) / Var(Y)]
        = [Cov(X,Y)]² / [Var(X) × Var(Y)]
        = [Cov(X,Y)]² / [√Var(X) × √Var(Y)]²
        = [Cov(X,Y) / (√Var(X) × √Var(Y))]²
        = [Corr(X,Y)]²
        = ρ²

### 结论

**β₁ × β₂ = ρ²**，其中ρ是X和Y的相关系数。

### 范围分析

由于相关系数ρ的范围是[-1, 1]，因此：
- ρ² ∈ [0, 1]
- **β₁ × β₂ ∈ [0, 1]**

### 特殊情况

1. **完全正相关或负相关** (|ρ| = 1)：
   - β₁ × β₂ = 1
   - 此时X和Y完全线性相关

2. **无相关性** (ρ = 0)：
   - β₁ × β₂ = 0
   - 此时X和Y线性无关

3. **一般情况** (0 < |ρ| < 1)：
   - 0 < β₁ × β₂ < 1

### 实际意义

- β₁ × β₂的值反映了X和Y之间线性关系的强度
- 值越接近1，说明线性关系越强
- 值越接近0，说明线性关系越弱
- 这个乘积总是非负的，因为它等于相关系数的平方

### Python验证示例

```python
import numpy as np
from scipy import stats

# 生成相关数据
np.random.seed(42)
x = np.random.normal(0, 1, 1000)
y = 0.7 * x + np.random.normal(0, 0.5, 1000)  # 相关系数约为0.8

# 计算回归系数
beta1 = np.cov(x, y)[0,1] / np.var(x)  # Y on X
beta2 = np.cov(x, y)[0,1] / np.var(y)  # X on Y

# 计算相关系数
correlation = np.corrcoef(x, y)[0,1]

print(f"β₁ = {beta1:.4f}")
print(f"β₂ = {beta2:.4f}")
print(f"β₁ × β₂ = {beta1 * beta2:.4f}")
print(f"ρ² = {correlation**2:.4f}")
print(f"验证: β₁ × β₂ = ρ² ? {np.isclose(beta1 * beta2, correlation**2)}")
```

### 总结

β₁ × β₂的范围是**[0, 1]**，且等于X和Y相关系数的平方，这是一个重要的统计学性质，在实际应用中可以用来评估变量间线性关系的强度。
