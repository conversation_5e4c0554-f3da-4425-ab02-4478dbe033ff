# 面试题6：缺失数据处理方法

## 题目
How to handle missing data? How to do imputation?
如何处理缺失数据？如何进行插补？
## 解答

### 缺失数据类型分析

#### 1. 完全随机缺失 (MCAR - Missing Completely At Random)
- **定义**：缺失与观测值和未观测值都无关
- **特点**：缺失概率对所有观测都相同
- **例子**：设备随机故障导致的数据丢失
- **处理**：简单删除不会引入偏差

#### 2. 随机缺失 (MAR - Missing At Random)
- **定义**：缺失与观测值有关，但与未观测值无关
- **特点**：给定观测变量，缺失是随机的
- **例子**：高收入人群更不愿透露收入信息
- **处理**：可以通过观测变量进行无偏插补

#### 3. 非随机缺失 (MNAR - Missing Not At Random)
- **定义**：缺失与未观测值本身有关
- **特点**：缺失机制依赖于缺失值
- **例子**：极高收入者拒绝报告收入
- **处理**：需要建模缺失机制

### 缺失数据处理策略

#### 1. 删除法 (Deletion Methods)

**列表删除 (Listwise Deletion)**
```python
# 删除任何变量有缺失的观测
df_complete = df.dropna()
```

**优点**：
- 简单易行
- 保持数据结构完整
- 在MCAR情况下无偏

**缺点**：
- 样本量大幅减少
- 在MAR/MNAR情况下有偏
- 损失信息

**成对删除 (Pairwise Deletion)**
```python
# 只在计算特定统计量时删除相关缺失
correlation_matrix = df.corr()  # 自动使用成对删除
```

#### 2. 单一插补法 (Single Imputation)

**均值/中位数/众数插补**
```python
from sklearn.impute import SimpleImputer

# 数值变量用均值
imputer_mean = SimpleImputer(strategy='mean')
df_numeric_imputed = imputer_mean.fit_transform(df_numeric)

# 分类变量用众数
imputer_mode = SimpleImputer(strategy='most_frequent')
df_categorical_imputed = imputer_mode.fit_transform(df_categorical)
```

**前向填充/后向填充**
```python
# 时间序列数据
df_filled = df.fillna(method='ffill')  # 前向填充
df_filled = df.fillna(method='bfill')  # 后向填充
```

**回归插补**
```python
from sklearn.linear_model import LinearRegression

def regression_imputation(df, target_col, predictor_cols):
    # 使用完整数据训练模型
    complete_mask = df[predictor_cols + [target_col]].notna().all(axis=1)
    X_train = df.loc[complete_mask, predictor_cols]
    y_train = df.loc[complete_mask, target_col]
    
    # 训练回归模型
    model = LinearRegression()
    model.fit(X_train, y_train)
    
    # 预测缺失值
    missing_mask = df[target_col].isna() & df[predictor_cols].notna().all(axis=1)
    X_missing = df.loc[missing_mask, predictor_cols]
    df.loc[missing_mask, target_col] = model.predict(X_missing)
    
    return df
```

#### 3. 多重插补法 (Multiple Imputation)

**MICE (Multiple Imputation by Chained Equations)**
```python
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer

# MICE插补
mice_imputer = IterativeImputer(random_state=42)
df_mice = pd.DataFrame(
    mice_imputer.fit_transform(df),
    columns=df.columns
)
```

**多重插补流程**：
1. 对每个缺失值创建m个插补数据集
2. 在每个数据集上进行分析
3. 合并结果（Rubin's Rules）

```python
import numpy as np

def multiple_imputation_analysis(df, m=5):
    """多重插补分析示例"""
    results = []
    
    for i in range(m):
        # 创建插补数据集
        imputer = IterativeImputer(random_state=i)
        df_imputed = pd.DataFrame(
            imputer.fit_transform(df),
            columns=df.columns
        )
        
        # 在插补数据上进行分析
        # 这里以线性回归为例
        from sklearn.linear_model import LinearRegression
        model = LinearRegression()
        X = df_imputed.drop('target', axis=1)
        y = df_imputed['target']
        model.fit(X, y)
        
        results.append({
            'coefficients': model.coef_,
            'intercept': model.intercept_
        })
    
    # 合并结果（简化版Rubin's Rules）
    avg_coef = np.mean([r['coefficients'] for r in results], axis=0)
    avg_intercept = np.mean([r['intercept'] for r in results])
    
    return avg_coef, avg_intercept
```

#### 4. 高级插补方法

**K近邻插补 (KNN Imputation)**
```python
from sklearn.impute import KNNImputer

knn_imputer = KNNImputer(n_neighbors=5)
df_knn = pd.DataFrame(
    knn_imputer.fit_transform(df),
    columns=df.columns
)
```

**矩阵分解插补**
```python
from sklearn.decomposition import TruncatedSVD
from sklearn.impute import IterativeImputer

class MatrixFactorizationImputer:
    def __init__(self, n_components=10):
        self.n_components = n_components
        self.svd = TruncatedSVD(n_components=n_components)
        
    def fit_transform(self, X):
        # 初始插补
        initial_imputer = SimpleImputer(strategy='mean')
        X_initial = initial_imputer.fit_transform(X)
        
        # SVD分解
        X_reduced = self.svd.fit_transform(X_initial)
        X_reconstructed = self.svd.inverse_transform(X_reduced)
        
        return X_reconstructed
```

### 插补质量评估

#### 1. 插补前后分布比较
```python
import matplotlib.pyplot as plt

def compare_distributions(original, imputed, column):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 原始数据分布
    original[column].hist(ax=ax1, alpha=0.7, bins=30)
    ax1.set_title('Original Data Distribution')
    
    # 插补后数据分布
    imputed[column].hist(ax=ax2, alpha=0.7, bins=30)
    ax2.set_title('Imputed Data Distribution')
    
    plt.tight_layout()
    plt.show()
```

#### 2. 插补不确定性分析
```python
def imputation_uncertainty(df, column, n_imputations=10):
    """分析插补的不确定性"""
    imputed_values = []
    
    for i in range(n_imputations):
        imputer = IterativeImputer(random_state=i)
        df_temp = df.copy()
        df_temp[column] = imputer.fit_transform(df[[column]])
        
        # 提取插补值
        missing_mask = df[column].isna()
        imputed_values.append(df_temp.loc[missing_mask, column].values)
    
    # 计算插补值的标准差
    imputed_array = np.array(imputed_values)
    uncertainty = np.std(imputed_array, axis=0)
    
    return uncertainty
```

### 实际应用指南

#### 选择插补方法的决策树

```python
def choose_imputation_method(df, missing_rate, data_type, missing_pattern):
    """插补方法选择指南"""
    
    if missing_rate < 0.05:
        return "列表删除"
    
    elif missing_rate < 0.15:
        if data_type == 'numerical':
            if missing_pattern == 'MCAR':
                return "均值插补"
            else:
                return "回归插补"
        else:
            return "众数插补"
    
    elif missing_rate < 0.30:
        return "MICE插补"
    
    else:
        return "考虑收集更多数据或使用专门的缺失数据模型"
```

#### 插补后的模型调整

```python
def post_imputation_analysis(df_original, df_imputed):
    """插补后分析调整"""
    
    # 1. 增加缺失指示变量
    for col in df_original.columns:
        if df_original[col].isna().any():
            df_imputed[f'{col}_was_missing'] = df_original[col].isna().astype(int)
    
    # 2. 调整标准误差（考虑插补不确定性）
    # 这在多重插补中特别重要
    
    # 3. 敏感性分析
    # 比较不同插补方法的结果
    
    return df_imputed
```

### 特殊情况处理

#### 时间序列缺失数据
```python
def time_series_imputation(ts_data):
    """时间序列专用插补"""
    
    # 线性插值
    ts_linear = ts_data.interpolate(method='linear')
    
    # 样条插值
    ts_spline = ts_data.interpolate(method='spline', order=3)
    
    # 季节性分解后插补
    from statsmodels.tsa.seasonal import seasonal_decompose
    decomposition = seasonal_decompose(ts_data.dropna(), model='additive')
    
    return ts_linear, ts_spline
```

#### 高维数据插补
```python
def high_dimensional_imputation(df):
    """高维数据插补策略"""
    
    # 1. 降维后插补
    from sklearn.decomposition import PCA
    
    # 2. 正则化回归插补
    from sklearn.linear_model import ElasticNet
    
    # 3. 随机森林插补
    from sklearn.ensemble import RandomForestRegressor
    
    pass
```

### 总结

| 方法类型 | 适用场景 | 优点 | 缺点 |
|----------|----------|------|------|
| 删除法 | 缺失率<5%, MCAR | 简单无偏 | 损失信息 |
| 简单插补 | 缺失率<15%, 快速分析 | 快速简单 | 低估不确定性 |
| 回归插补 | MAR, 有相关变量 | 利用变量关系 | 可能过拟合 |
| MICE | 复杂缺失模式 | 处理任意缺失 | 计算复杂 |
| KNN插补 | 非线性关系 | 非参数方法 | 计算量大 |

**最佳实践**：
1. 首先分析缺失机制
2. 根据缺失率和模式选择方法
3. 进行敏感性分析
4. 考虑插补不确定性
5. 验证插补质量
