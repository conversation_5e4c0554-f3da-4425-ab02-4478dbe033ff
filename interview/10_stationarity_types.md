# 面试题10：平稳性类型对比

## 题目
Weak stationary vs Strong stationary
弱平稳性与强平稳性的区别

## 解答

### 基本定义

#### 强平稳性 (Strong Stationarity / Strict Stationarity)

**定义**：时间序列{Xₜ}的任意有限维联合分布在时间平移下保持不变。

**数学表述**：
对于任意正整数n，任意时间点t₁, t₂, ..., tₙ和任意时间间隔h：
```
F(x₁, x₂, ..., xₙ; t₁, t₂, ..., tₙ) = F(x₁, x₂, ..., xₙ; t₁+h, t₂+h, ..., tₙ+h)
```

即：(Xₜ₁, Xₜ₂, ..., Xₜₙ) 与 (Xₜ₁₊ₕ, Xₜ₂₊ₕ, ..., Xₜₙ₊ₕ) 具有相同的联合分布。

#### 弱平稳性 (Weak Stationarity / Covariance Stationarity)

**定义**：时间序列的一阶矩和二阶矩在时间平移下保持不变。

**数学表述**：
1. **常数均值**：E[Xₜ] = μ (常数，不依赖于t)
2. **有限方差**：Var(Xₜ) = σ² < ∞ (常数，不依赖于t)  
3. **协方差只依赖于时间间隔**：Cov(Xₜ, Xₜ₊ₕ) = γ(h) (只依赖于h，不依赖于t)

### 关系分析

#### 逻辑关系
- **强平稳 ⟹ 弱平稳**（在二阶矩存在的条件下）
- **弱平稳 ⟹ 强平稳**（一般不成立）

#### 反例：弱平稳但非强平稳

```python
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
import pandas as pd

def weak_not_strong_example():
    """弱平稳但非强平稳的例子"""
    
    np.random.seed(42)
    n = 1000
    
    # 生成弱平稳但非强平稳的序列
    # 使用时变的分布但保持前两阶矩不变
    
    t = np.arange(n)
    
    # 方法1：混合正态分布，权重随时间变化但均值方差不变
    X = np.zeros(n)
    for i in range(n):
        # 时变权重，但保证总体均值和方差不变
        p = 0.5 + 0.3 * np.sin(2 * np.pi * i / 100)  # 周期性变化的权重
        
        if np.random.random() < p:
            X[i] = np.random.normal(0, 1)  # 标准正态
        else:
            # 调整第二个分布使总体均值为0，方差为1
            X[i] = np.random.normal(0, 1)
    
    # 更明显的例子：时变偏度
    X_skewed = np.zeros(n)
    for i in range(n):
        # 使用偏态分布，偏度随时间变化
        skewness = 2 * np.sin(2 * np.pi * i / 200)
        
        # 生成偏态分布但保持均值为0，方差为1
        if skewness > 0:
            # 右偏
            raw_sample = np.random.gamma(2, 1) - 2  # 调整使均值约为0
        else:
            # 左偏
            raw_sample = -(np.random.gamma(2, 1) - 2)
        
        # 标准化使方差为1
        X_skewed[i] = raw_sample
    
    # 标准化使其严格满足弱平稳条件
    X_skewed = (X_skewed - np.mean(X_skewed)) / np.std(X_skewed)
    
    return t, X, X_skewed

def analyze_stationarity(X, title="时间序列"):
    """分析序列的平稳性"""
    
    n = len(X)
    
    # 计算滑动统计量
    window = 100
    rolling_mean = pd.Series(X).rolling(window=window).mean()
    rolling_std = pd.Series(X).rolling(window=window).std()
    rolling_skew = pd.Series(X).rolling(window=window).skew()
    rolling_kurt = pd.Series(X).rolling(window=window).kurtosis()
    
    # 可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 原始序列
    axes[0,0].plot(X)
    axes[0,0].set_title(f'{title} - 原始序列')
    axes[0,0].set_ylabel('值')
    axes[0,0].grid(True, alpha=0.3)
    
    # 滑动均值和标准差
    axes[0,1].plot(rolling_mean, label='滑动均值', color='blue')
    axes[0,1].axhline(y=np.mean(X), color='blue', linestyle='--', alpha=0.7)
    ax_twin = axes[0,1].twinx()
    ax_twin.plot(rolling_std, label='滑动标准差', color='red')
    ax_twin.axhline(y=np.std(X), color='red', linestyle='--', alpha=0.7)
    axes[0,1].set_title('滑动均值和标准差')
    axes[0,1].legend(loc='upper left')
    ax_twin.legend(loc='upper right')
    axes[0,1].grid(True, alpha=0.3)
    
    # 滑动偏度
    axes[1,0].plot(rolling_skew, color='green')
    axes[1,0].axhline(y=stats.skew(X), color='green', linestyle='--', alpha=0.7)
    axes[1,0].set_title('滑动偏度')
    axes[1,0].set_ylabel('偏度')
    axes[1,0].grid(True, alpha=0.3)
    
    # 滑动峰度
    axes[1,1].plot(rolling_kurt, color='purple')
    axes[1,1].axhline(y=stats.kurtosis(X), color='purple', linestyle='--', alpha=0.7)
    axes[1,1].set_title('滑动峰度')
    axes[1,1].set_ylabel('峰度')
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 统计检验
    print(f"\n{title} 统计特征:")
    print(f"均值: {np.mean(X):.4f}")
    print(f"标准差: {np.std(X):.4f}")
    print(f"偏度: {stats.skew(X):.4f}")
    print(f"峰度: {stats.kurtosis(X):.4f}")
    
    # 检验均值和方差的稳定性
    first_half = X[:len(X)//2]
    second_half = X[len(X)//2:]
    
    print(f"\n前半段均值: {np.mean(first_half):.4f}")
    print(f"后半段均值: {np.mean(second_half):.4f}")
    print(f"前半段标准差: {np.std(first_half):.4f}")
    print(f"后半段标准差: {np.std(second_half):.4f}")
    
    # t检验检验均值差异
    t_stat, p_value = stats.ttest_ind(first_half, second_half)
    print(f"均值差异t检验 p值: {p_value:.4f}")

# 运行示例
t, X_normal, X_skewed = weak_not_strong_example()
analyze_stationarity(X_skewed, "弱平稳但非强平稳序列")
```

### 实际例子对比

#### 1. 白噪声（强平稳）

```python
def white_noise_example():
    """白噪声：强平稳的典型例子"""
    
    np.random.seed(42)
    n = 1000
    
    # 高斯白噪声
    white_noise = np.random.normal(0, 1, n)
    
    print("白噪声（强平稳）:")
    analyze_stationarity(white_noise, "白噪声")
    
    return white_noise

white_noise = white_noise_example()
```

#### 2. AR(1)过程（弱平稳条件）

```python
def ar1_process_example():
    """AR(1)过程的平稳性分析"""
    
    def generate_ar1(n, phi, sigma=1, x0=0):
        """生成AR(1)过程: X_t = phi * X_{t-1} + epsilon_t"""
        X = np.zeros(n)
        X[0] = x0
        
        for t in range(1, n):
            X[t] = phi * X[t-1] + np.random.normal(0, sigma)
        
        return X
    
    n = 1000
    
    # 平稳AR(1): |phi| < 1
    phi_stationary = 0.7
    X_stationary = generate_ar1(n, phi_stationary)
    
    # 非平稳AR(1): |phi| >= 1
    phi_nonstationary = 1.02
    X_nonstationary = generate_ar1(n, phi_nonstationary)
    
    print(f"AR(1)过程比较:")
    print(f"平稳条件: |φ| < 1")
    print(f"φ = {phi_stationary} (平稳)")
    analyze_stationarity(X_stationary, f"AR(1) φ={phi_stationary}")
    
    print(f"\nφ = {phi_nonstationary} (非平稳)")
    analyze_stationarity(X_nonstationary, f"AR(1) φ={phi_nonstationary}")
    
    return X_stationary, X_nonstationary

ar1_stationary, ar1_nonstationary = ar1_process_example()
```

#### 3. 趋势序列（非平稳）

```python
def trend_series_example():
    """含趋势的非平稳序列"""
    
    np.random.seed(42)
    n = 1000
    t = np.arange(n)
    
    # 线性趋势 + 噪声
    linear_trend = 0.01 * t + np.random.normal(0, 1, n)
    
    # 二次趋势 + 噪声
    quadratic_trend = 0.0001 * t**2 + np.random.normal(0, 1, n)
    
    print("含趋势序列（非平稳）:")
    analyze_stationarity(linear_trend, "线性趋势序列")
    analyze_stationarity(quadratic_trend, "二次趋势序列")
    
    return linear_trend, quadratic_trend

linear_trend, quadratic_trend = trend_series_example()
```

### 平稳性检验

#### 1. Augmented Dickey-Fuller (ADF) 检验

```python
from statsmodels.tsa.stattools import adfuller

def adf_test(series, title="序列"):
    """ADF平稳性检验"""
    
    result = adfuller(series, autolag='AIC')
    
    print(f'\n{title} ADF检验结果:')
    print(f'ADF统计量: {result[0]:.6f}')
    print(f'p值: {result[1]:.6f}')
    print(f'临界值:')
    for key, value in result[4].items():
        print(f'\t{key}: {value:.3f}')
    
    if result[1] <= 0.05:
        print("结论: 拒绝原假设，序列是平稳的")
    else:
        print("结论: 不能拒绝原假设，序列可能是非平稳的")
    
    return result

# 对不同序列进行ADF检验
print("=== ADF平稳性检验 ===")
adf_test(white_noise, "白噪声")
adf_test(ar1_stationary, "平稳AR(1)")
adf_test(ar1_nonstationary, "非平稳AR(1)")
adf_test(linear_trend, "线性趋势")
```

#### 2. KPSS检验

```python
from statsmodels.tsa.stattools import kpss

def kpss_test(series, title="序列"):
    """KPSS平稳性检验"""
    
    statistic, p_value, n_lags, critical_values = kpss(series, regression='c')
    
    print(f'\n{title} KPSS检验结果:')
    print(f'KPSS统计量: {statistic:.6f}')
    print(f'p值: {p_value:.6f}')
    print(f'滞后阶数: {n_lags}')
    print(f'临界值:')
    for key, value in critical_values.items():
        print(f'\t{key}: {value:.3f}')
    
    if p_value <= 0.05:
        print("结论: 拒绝原假设，序列是非平稳的")
    else:
        print("结论: 不能拒绝原假设，序列是平稳的")

# KPSS检验
print("\n=== KPSS平稳性检验 ===")
kpss_test(white_noise, "白噪声")
kpss_test(ar1_stationary, "平稳AR(1)")
kpss_test(linear_trend, "线性趋势")
```

### 实际应用中的考虑

#### 1. 金融时间序列

```python
def financial_series_stationarity():
    """金融时间序列的平稳性特征"""
    
    # 模拟股价（非平稳）和收益率（通常平稳）
    np.random.seed(42)
    n = 1000
    
    # 股价：随机游走（非平稳）
    returns = np.random.normal(0.001, 0.02, n)  # 日收益率
    prices = np.zeros(n)
    prices[0] = 100
    
    for t in range(1, n):
        prices[t] = prices[t-1] * (1 + returns[t])
    
    # 对数收益率
    log_returns = np.diff(np.log(prices))
    
    print("金融时间序列平稳性分析:")
    analyze_stationarity(prices, "股价序列")
    analyze_stationarity(log_returns, "对数收益率序列")
    
    # 平稳性检验
    adf_test(prices, "股价")
    adf_test(log_returns, "对数收益率")
    
    return prices, log_returns

prices, log_returns = financial_series_stationarity()
```

#### 2. 协整关系

```python
def cointegration_example():
    """协整关系：非平稳序列的线性组合可能平稳"""
    
    np.random.seed(42)
    n = 1000
    
    # 生成两个协整的非平稳序列
    # 共同趋势
    common_trend = np.cumsum(np.random.normal(0, 1, n))
    
    # 两个序列
    X1 = common_trend + np.random.normal(0, 0.5, n)
    X2 = 2 * common_trend + np.random.normal(0, 0.5, n)
    
    # 协整向量 [1, -2]，使得 X1 - 2*X2 平稳
    cointegrating_combination = X1 - 0.5 * X2
    
    print("协整关系示例:")
    analyze_stationarity(X1, "序列X1")
    analyze_stationarity(X2, "序列X2") 
    analyze_stationarity(cointegrating_combination, "协整组合 X1 - 0.5*X2")
    
    # 检验
    adf_test(X1, "X1")
    adf_test(X2, "X2")
    adf_test(cointegrating_combination, "协整组合")
    
    return X1, X2, cointegrating_combination

X1, X2, coint_combo = cointegration_example()
```

### 平稳化方法

```python
def stationarity_transformation():
    """常用的平稳化方法"""
    
    # 使用线性趋势序列作为例子
    np.random.seed(42)
    n = 500
    t = np.arange(n)
    
    # 原始非平稳序列
    original = 0.02 * t + 0.001 * t**2 + np.random.normal(0, 1, n)
    
    # 方法1：一阶差分
    first_diff = np.diff(original)
    
    # 方法2：去趋势
    from sklearn.linear_model import LinearRegression
    reg = LinearRegression()
    reg.fit(t.reshape(-1, 1), original)
    trend = reg.predict(t.reshape(-1, 1))
    detrended = original - trend
    
    # 方法3：对数变换（如果序列为正）
    if np.all(original > 0):
        log_transformed = np.log(original)
    else:
        # 平移使其为正
        shifted = original - np.min(original) + 1
        log_transformed = np.log(shifted)
    
    # 可视化对比
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    axes[0,0].plot(original)
    axes[0,0].set_title('原始序列（非平稳）')
    axes[0,0].grid(True, alpha=0.3)
    
    axes[0,1].plot(first_diff)
    axes[0,1].set_title('一阶差分')
    axes[0,1].grid(True, alpha=0.3)
    
    axes[1,0].plot(detrended)
    axes[1,0].set_title('去趋势')
    axes[1,0].grid(True, alpha=0.3)
    
    axes[1,1].plot(log_transformed)
    axes[1,1].set_title('对数变换')
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 检验平稳性
    print("平稳化方法效果比较:")
    adf_test(original, "原始序列")
    adf_test(first_diff, "一阶差分")
    adf_test(detrended, "去趋势")
    adf_test(log_transformed, "对数变换")

stationarity_transformation()
```

### 总结对比表

| 特征 | 强平稳性 | 弱平稳性 |
|------|----------|----------|
| **定义范围** | 所有阶矩和分布 | 仅前两阶矩 |
| **数学要求** | 联合分布不变 | 均值、方差、协方差不变 |
| **检验难度** | 很难直接检验 | 相对容易检验 |
| **实际应用** | 理论分析 | 实际建模 |
| **典型例子** | 白噪声 | AR、MA过程 |
| **包含关系** | 强⟹弱 | 弱⟹强（一般不成立） |

### 实际应用指南

```python
def stationarity_checklist():
    """平稳性检验清单"""
    
    checklist = {
        "数据预处理": [
            "检查缺失值和异常值",
            "绘制时间序列图",
            "计算基本统计量"
        ],
        "视觉检验": [
            "时间序列图是否显示趋势",
            "滚动统计量是否稳定",
            "ACF/PACF图的衰减模式"
        ],
        "统计检验": [
            "ADF检验（原假设：非平稳）",
            "KPSS检验（原假设：平稳）",
            "PP检验（Phillips-Perron）"
        ],
        "平稳化处理": [
            "差分（一阶、二阶）",
            "去趋势（线性、非线性）",
            "对数变换",
            "季节调整"
        ],
        "模型选择": [
            "平稳序列：ARMA模型",
            "非平稳序列：ARIMA模型",
            "协整序列：VECM模型"
        ]
    }
    
    print("时间序列平稳性分析清单:")
    print("="*40)
    
    for category, items in checklist.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  □ {item}")

stationarity_checklist()
```

**关键要点**：
1. **强平稳性要求更严格**，在实际中很难满足
2. **弱平稳性是时间序列建模的基础**
3. **大多数经济金融序列需要平稳化处理**
4. **平稳性检验应结合多种方法**
5. **平稳化是为了满足模型假设，提高预测精度**
