# 面试题4：多元回归R²的范围

## 题目
Regression Y~X1, get R²₁ as 0.3, Regression Y~X2 get R²₂ as 0.4. What's the range of R² when we regress Y~X1+X2?
回归Y~X1得到R²₁=0.3，回归Y~X2得到R²₂=0.4。当我们回归Y~X1+X2时，R²的范围是什么？
## 解答

### 问题分析

给定条件：
- 单变量回归 Y ~ X₁：R²₁ = 0.3
- 单变量回归 Y ~ X₂：R²₂ = 0.4
- 求多元回归 Y ~ X₁ + X₂ 的R²范围

### 理论基础

#### R²的性质
1. **单调性**：添加变量不会降低R²
2. **上界**：R² ≤ 1
3. **下界**：R² ≥ max(R²₁, R²₂)

#### 关键因素：X₁和X₂的相关性

多元回归的R²取决于：
- 各自变量与因变量的相关性
- 自变量之间的相关性（多重共线性）

### 数学推导

#### 多元回归R²公式

对于回归 Y = β₀ + β₁X₁ + β₂X₂ + ε：

R² = (β₁²Var(X₁) + β₂²Var(X₂) + 2β₁β₂Cov(X₁,X₂)) / Var(Y)

#### 与单变量回归的关系

设：
- r₁ = Corr(Y, X₁)，则 R²₁ = r₁² = 0.3，所以 |r₁| = √0.3 ≈ 0.548
- r₂ = Corr(Y, X₂)，则 R²₂ = r₂² = 0.4，所以 |r₂| = √0.4 ≈ 0.632
- r₁₂ = Corr(X₁, X₂)

#### 多元回归R²的精确公式

R² = (r₁² + r₂² - 2r₁r₂r₁₂) / (1 - r₁₂²)

### 范围分析

#### 下界：max(R²₁, R²₂) = 0.4

**证明：**
添加变量不会降低R²，因此：
R²(Y~X₁+X₂) ≥ max(R²(Y~X₁), R²(Y~X₂)) = max(0.3, 0.4) = 0.4

#### 上界分析

上界取决于X₁和X₂的相关性r₁₂：

**情况1：完全正相关 (r₁₂ = 1)**
- 此时X₁和X₂提供相同信息
- R² = max(R²₁, R²₂) = 0.4

**情况2：完全负相关 (r₁₂ = -1)**
- 此时X₁和X₂提供相同信息（符号相反）
- R² = max(R²₁, R²₂) = 0.4

**情况3：无相关 (r₁₂ = 0)**
- 此时X₁和X₂提供独立信息
- R² = R²₁ + R²₂ = 0.3 + 0.4 = 0.7

**情况4：最优相关性**
通过求导可以找到使R²最大的r₁₂值。

### 精确计算

#### 考虑符号的影响

由于我们只知道R²₁和R²₂，需要考虑r₁和r₂的符号组合：

**组合1：r₁ > 0, r₂ > 0**
- r₁ = √0.3 ≈ 0.548, r₂ = √0.4 ≈ 0.632

**组合2：r₁ > 0, r₂ < 0**
- r₁ = √0.3 ≈ 0.548, r₂ = -√0.4 ≈ -0.632

**组合3：r₁ < 0, r₂ > 0**
- r₁ = -√0.3 ≈ -0.548, r₂ = √0.4 ≈ 0.632

**组合4：r₁ < 0, r₂ < 0**
- r₁ = -√0.3 ≈ -0.548, r₂ = -√0.4 ≈ -0.632

#### 最大R²计算

对于每种符号组合，最大R²出现在r₁₂使分子最大时：

当r₁和r₂同号时，最大值在r₁₂ = (r₁r₂)/(r₁² + r₂²) × 某个因子时达到。

通过数值计算，可以得到：
**R²max ≈ 0.7**（当r₁₂ = 0时达到）

### Python验证

```python
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def calculate_r_squared(r1, r2, r12):
    """计算多元回归R²"""
    numerator = r1**2 + r2**2 - 2*r1*r2*r12
    denominator = 1 - r12**2
    if abs(denominator) < 1e-10:  # 避免除零
        return np.nan
    return numerator / denominator

# 给定条件
R2_1 = 0.3
R2_2 = 0.4
r1_abs = np.sqrt(R2_1)  # 0.548
r2_abs = np.sqrt(R2_2)  # 0.632

# 考虑所有符号组合
sign_combinations = [(1, 1), (1, -1), (-1, 1), (-1, -1)]
r12_range = np.linspace(-0.99, 0.99, 1000)

results = {}
for i, (sign1, sign2) in enumerate(sign_combinations):
    r1 = sign1 * r1_abs
    r2 = sign2 * r2_abs
    
    r_squared_values = []
    for r12 in r12_range:
        r_sq = calculate_r_squared(r1, r2, r12)
        r_squared_values.append(r_sq)
    
    r_squared_values = np.array(r_squared_values)
    valid_mask = ~np.isnan(r_squared_values) & (r_squared_values >= 0) & (r_squared_values <= 1)
    
    if np.any(valid_mask):
        results[f'组合{i+1} (r1={sign1}√0.3, r2={sign2}√0.4)'] = {
            'min': np.min(r_squared_values[valid_mask]),
            'max': np.max(r_squared_values[valid_mask]),
            'r12_for_max': r12_range[valid_mask][np.argmax(r_squared_values[valid_mask])]
        }

print("多元回归R²范围分析:")
print("="*50)

overall_min = float('inf')
overall_max = 0

for combo, stats in results.items():
    print(f"{combo}:")
    print(f"  最小R²: {stats['min']:.4f}")
    print(f"  最大R²: {stats['max']:.4f}")
    print(f"  最大值对应的r₁₂: {stats['r12_for_max']:.4f}")
    print()
    
    overall_min = min(overall_min, stats['min'])
    overall_max = max(overall_max, stats['max'])

print(f"总体范围: [{overall_max:.1f}, {overall_max:.1f}]")
print(f"理论下界: {max(R2_1, R2_2)}")

# 特殊情况验证
print("\n特殊情况验证:")
print(f"r₁₂ = 0 (无相关): R² = {R2_1 + R2_2}")
print(f"r₁₂ = ±1 (完全相关): R² = {max(R2_1, R2_2)}")
```

### 实际应用考虑

#### 现实约束
1. **变量选择**：实际中很少有完全相关的变量
2. **多重共线性**：高相关性会导致估计不稳定
3. **样本大小**：小样本中R²可能过度拟合

#### 经验法则
- 当|r₁₂| < 0.3时，R² ≈ R²₁ + R²₂
- 当|r₁₂| > 0.8时，R² ≈ max(R²₁, R²₂)
- 最佳情况通常在r₁₂ = 0附近

### 总结

**多元回归Y ~ X₁ + X₂的R²范围：[0.4, 0.7]**

| 情况 | r₁₂ | R² |
|------|-----|-----|
| 完全相关 | ±1 | 0.4 |
| 无相关 | 0 | 0.7 |
| 一般情况 | (-1,1) | [0.4, 0.7] |

**关键洞察：**
1. 下界由较大的单变量R²决定
2. 上界在变量无相关时达到
3. 变量间相关性越高，多元回归的改善越小
4. 实际应用中需要平衡解释力和模型复杂度
