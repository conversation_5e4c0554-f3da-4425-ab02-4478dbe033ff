<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化面试题小红书卡片</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
        }
        
        .card {
            width: 480px;
            min-height: 650px;
            height: auto;
            margin: 20px;
            border-radius: 25px;
            padding: 45px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            position: relative;
            overflow: visible;
            color: #2d3748;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            pointer-events: none;
            border-radius: 25px;
        }
        
        /* 不同卡片的渐变色 */
        .card-1 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%); }
        .card-2 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .card-3 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .card-4 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
        .card-5 { background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); }
        .card-6 { background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%); }
        .card-7 { background: linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%); }
        .card-8 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .card-9 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .card-10 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
        
        .card-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .card-number {
            background: rgba(255,255,255,0.9);
            color: #667eea;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .card-title {
            font-size: 28px;
            font-weight: bold;
            line-height: 1.3;
            margin: 0;
        }
        
        .card-content {
            font-size: 19px;
            line-height: 1.8;
            margin-bottom: 30px;
        }
        
        .highlight {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .formula {
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 12px;
            font-family: 'Courier New', monospace;
            text-align: center;
            margin: 15px 0;
            font-weight: bold;
            color: #e53e3e;
            font-size: 20px;
        }
        
        .key-points {
            background: rgba(255,255,255,0.7);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .key-points ul {
            margin: 10px 0;
            padding-left: 25px;
        }
        
        .key-points li {
            margin: 12px 0;
            font-size: 18px;
        }
        
        .tag {
            background: rgba(255,255,255,0.8);
            color: #667eea;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 14px;
            margin: 3px;
            display: inline-block;
            font-weight: 500;
        }
        
        .footer {
            position: absolute;
            bottom: 25px;
            left: 45px;
            right: 45px;
            text-align: center;
            font-size: 14px;
            opacity: 0.8;
            font-weight: 500;
        }
        
        .emoji {
            font-size: 22px;
            margin-right: 10px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .card {
                width: 90%;
                min-height: 550px;
                padding: 35px;
                margin: 15px auto;
            }
            
            .card-title {
                font-size: 24px;
            }
            
            .card-content {
                font-size: 17px;
            }
            
            .container {
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        
        <!-- 题目1：回归系数乘积范围 -->
        <div class="card card-1">
            <div class="card-header">
                <div class="card-number">📊 面试题 01</div>
                <h2 class="card-title">回归系数乘积的范围</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>对Y关于X回归得β₁，对X关于Y回归得β₂，求β₁×β₂的范围？</p>
                
                <div class="highlight">
                    <div class="formula">β₁ × β₂ = ρ²</div>
                    <p style="text-align: center; margin: 8px 0; font-size: 20px;"><strong>范围：[0, 1]</strong></p>
                </div>
                
                <div class="key-points">
                    <strong>💡 核心要点：</strong>
                    <ul>
                        <li>等于相关系数的平方</li>
                        <li>反映线性关系强度</li>
                        <li>完全相关时 = 1</li>
                        <li>无相关时 = 0</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#量化面试</span>
                    <span class="tag">#回归分析</span>
                    <span class="tag">#统计学</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">📈</span>量化研究必备知识点
            </div>
        </div>
        
        <!-- 题目2：重复数据影响 -->
        <div class="card card-2">
            <div class="card-header">
                <div class="card-number">📊 面试题 02</div>
                <h2 class="card-title">重复数据对回归的影响</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>将X和Y数据重复一遍，如何影响β、R²和p值？</p>
                
                <div class="key-points">
                    <strong>📋 影响结果：</strong>
                    <ul>
                        <li><strong>β系数：</strong>不变 ✅</li>
                        <li><strong>R²：</strong>不变 ✅</li>
                        <li><strong>p值：</strong>显著降低 ⚠️</li>
                        <li><strong>标准误差：</strong>减小1/√2</li>
                    </ul>
                </div>
                
                <div class="highlight">
                    <strong>⚠️ 关键警示：</strong><br>
                    重复数据会产生虚假的统计显著性！这是数据预处理中需要特别注意的陷阱。
                </div>
                
                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#数据清洗</span>
                    <span class="tag">#统计陷阱</span>
                    <span class="tag">#回归分析</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">⚠️</span>数据质量是关键
            </div>
        </div>
        
        <!-- 题目3：R²符号性质 -->
        <div class="card card-3">
            <div class="card-header">
                <div class="card-number">📊 面试题 03</div>
                <h2 class="card-title">R²的符号性质</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>线性回归中R²总是正数吗？无截距项会怎样？</p>
                
                <div class="key-points">
                    <strong>📋 答案总结：</strong>
                    <ul>
                        <li><strong>有截距：</strong>R² ∈ [0, 1] ✅</li>
                        <li><strong>无截距：</strong>R² ∈ (-∞, 1] ⚠️</li>
                    </ul>
                </div>
                
                <div class="highlight">
                    <strong>💡 关键洞察：</strong><br>
                    无截距回归的负R²表明模型比简单均值模型表现更差，说明模型设定不当！
                </div>
                
                <div class="formula">
                    R² = 1 - SSE/SST
                </div>
                
                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#模型诊断</span>
                    <span class="tag">#回归分析</span>
                    <span class="tag">#统计学</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">📐</span>模型设定很重要
            </div>
        </div>

        <!-- 题目4：多元回归R²范围 -->
        <div class="card card-4">
            <div class="card-header">
                <div class="card-number">📊 面试题 04</div>
                <h2 class="card-title">多元回归R²的范围</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Y~X1得R²₁=0.3，Y~X2得R²₂=0.4，Y~X1+X2的R²范围？</p>

                <div class="highlight">
                    <div class="formula">范围：[0.4, 0.7]</div>
                </div>

                <div class="key-points">
                    <strong>📋 范围分析：</strong>
                    <ul>
                        <li><strong>下界：</strong>max(R²₁, R²₂) = 0.4</li>
                        <li><strong>上界：</strong>X₁、X₂无相关时 = 0.7</li>
                        <li><strong>影响因素：</strong>变量间相关性</li>
                    </ul>
                </div>

                <div class="highlight">
                    <strong>💡 关键洞察：</strong><br>
                    变量间相关性越高，多元回归的改善越小！
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#多元回归</span>
                    <span class="tag">#变量选择</span>
                    <span class="tag">#多重共线性</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🔗</span>变量关系是关键
            </div>
        </div>

        <!-- 题目5：误差偏差分析 -->
        <div class="card card-5">
            <div class="card-header">
                <div class="card-number">📊 面试题 05</div>
                <h2 class="card-title">误差项对模型偏差的影响</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Y=WX+b+e 和 Y=W(X+e)+b 中，e如何影响偏差？</p>

                <div class="key-points">
                    <strong>📋 影响对比：</strong>
                    <ul>
                        <li><strong>因变量误差：</strong>无偏估计 ✅</li>
                        <li><strong>自变量误差：</strong>衰减偏差 ⚠️</li>
                    </ul>
                </div>

                <div class="highlight">
                    <div class="formula">衰减因子：λ = Var(X) / [Var(X) + Var(e)]</div>
                </div>

                <div class="highlight">
                    <strong>💡 关键洞察：</strong><br>
                    测量误差会导致系数向零偏移，这是计量经济学中的重要问题！
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#测量误差</span>
                    <span class="tag">#计量经济学</span>
                    <span class="tag">#偏差分析</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🎯</span>测量精度很重要
            </div>
        </div>

        <!-- 题目6：缺失数据处理 -->
        <div class="card card-6">
            <div class="card-header">
                <div class="card-number">📊 面试题 06</div>
                <h2 class="card-title">缺失数据处理方法</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>如何处理缺失数据？如何进行插补？</p>

                <div class="key-points">
                    <strong>📋 缺失机制：</strong>
                    <ul>
                        <li><strong>MCAR：</strong>完全随机缺失</li>
                        <li><strong>MAR：</strong>随机缺失</li>
                        <li><strong>MNAR：</strong>非随机缺失</li>
                    </ul>
                </div>

                <div class="key-points">
                    <strong>🛠️ 处理方法：</strong>
                    <ul>
                        <li>删除法（列表/成对删除）</li>
                        <li>简单插补（均值/中位数/众数）</li>
                        <li>高级插补（MICE/KNN/回归）</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#数据预处理</span>
                    <span class="tag">#缺失值</span>
                    <span class="tag">#数据清洗</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🔧</span>数据预处理是基础
            </div>
        </div>

        <!-- 题目7：密度拟合策略 -->
        <div class="card card-7">
            <div class="card-header">
                <div class="card-number">📊 面试题 07</div>
                <h2 class="card-title">不同密度区域拟合策略</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>不同区域具有不同密度的随机点，最佳拟合策略？</p>

                <div class="key-points">
                    <strong>🎯 策略选择：</strong>
                    <ul>
                        <li><strong>加权拟合：</strong>根据密度分配权重</li>
                        <li><strong>局部拟合：</strong>LOESS、局部多项式</li>
                        <li><strong>分段拟合：</strong>自适应分段</li>
                        <li><strong>多尺度：</strong>小波、高斯过程</li>
                    </ul>
                </div>

                <div class="highlight">
                    <strong>💡 选择原则：</strong><br>
                    根据密度变化程度、数据量和计算资源选择最适合的策略
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#非参数回归</span>
                    <span class="tag">#局部拟合</span>
                    <span class="tag">#自适应方法</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🎨</span>因地制宜很重要
            </div>
        </div>

        <!-- 题目8：Lasso vs Ridge -->
        <div class="card card-8">
            <div class="card-header">
                <div class="card-number">📊 面试题 08</div>
                <h2 class="card-title">Lasso与Ridge回归对比</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Lasso与Ridge的区别</p>

                <div class="key-points">
                    <strong>🔍 核心差异：</strong>
                    <ul>
                        <li><strong>Ridge (L2)：</strong>连续收缩，处理共线性</li>
                        <li><strong>Lasso (L1)：</strong>稀疏化，自动特征选择</li>
                        <li><strong>Elastic Net：</strong>结合两者优点</li>
                    </ul>
                </div>

                <div class="highlight">
                    <div class="formula">Ridge: ||β||²₂ vs Lasso: ||β||₁</div>
                </div>

                <div class="highlight">
                    <strong>💡 选择指南：</strong><br>
                    预测为主选Ridge，特征选择选Lasso，兼顾选Elastic Net
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#正则化</span>
                    <span class="tag">#特征选择</span>
                    <span class="tag">#机器学习</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">⚖️</span>正则化是平衡艺术
            </div>
        </div>

        <!-- 题目9：集成学习对比 -->
        <div class="card card-9">
            <div class="card-header">
                <div class="card-number">📊 面试题 09</div>
                <h2 class="card-title">集成学习方法对比</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>Bagging与Boosting的区别，XGBoost与GBM的区别</p>

                <div class="key-points">
                    <strong>🔄 Bagging vs Boosting：</strong>
                    <ul>
                        <li><strong>Bagging：</strong>并行训练，降方差</li>
                        <li><strong>Boosting：</strong>串行训练，降偏差</li>
                    </ul>
                </div>

                <div class="key-points">
                    <strong>🚀 XGBoost vs GBM：</strong>
                    <ul>
                        <li><strong>GBM：</strong>一阶梯度，基础正则化</li>
                        <li><strong>XGBoost：</strong>二阶优化，工程优化</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#集成学习</span>
                    <span class="tag">#随机森林</span>
                    <span class="tag">#梯度提升</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">🤝</span>团结就是力量
            </div>
        </div>

        <!-- 题目10：平稳性类型 -->
        <div class="card card-10">
            <div class="card-header">
                <div class="card-number">📊 面试题 10</div>
                <h2 class="card-title">弱平稳性与强平稳性</h2>
            </div>
            <div class="card-content">
                <p><strong>🤔 题目：</strong>弱平稳性与强平稳性的区别</p>

                <div class="key-points">
                    <strong>📋 定义对比：</strong>
                    <ul>
                        <li><strong>强平稳：</strong>所有阶矩和分布不变</li>
                        <li><strong>弱平稳：</strong>仅前两阶矩不变</li>
                    </ul>
                </div>

                <div class="highlight">
                    <div class="formula">强平稳 ⟹ 弱平稳</div>
                    <p style="text-align: center; margin: 8px 0; font-size: 18px;">反之不成立</p>
                </div>

                <div class="highlight">
                    <strong>💡 实际应用：</strong><br>
                    弱平稳性是时间序列建模（ARIMA）的基础假设
                </div>

                <div style="text-align: center; margin-top: 25px;">
                    <span class="tag">#时间序列</span>
                    <span class="tag">#平稳性</span>
                    <span class="tag">#ARIMA</span>
                </div>
            </div>
            <div class="footer">
                <span class="emoji">📈</span>时间序列的基石
            </div>
        </div>

    </div>
</body>
</html>
