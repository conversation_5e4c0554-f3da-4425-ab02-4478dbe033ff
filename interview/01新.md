# 面试题1：回归系数乘积的范围（详细计算版）

## 题目
Regress Y on X get beta1, regress X on Y get beta 2. What is the range of beta1*beta2?

对Y关于X进行回归得到系数beta1，对X关于Y进行回归得到系数beta2。请问beta1*beta2的取值范围是什么？

## 解答

### 数学推导

设我们有两个回归方程：
1. Y = α₁ + β₁X + ε₁  (Y对X回归)
2. X = α₂ + β₂Y + ε₂  (X对Y回归)

### 各统计量的具体计算步骤

#### 1. 回归系数β₁的计算

**最小二乘法推导：**

目标：最小化 SSE₁ = Σ(Yᵢ - α₁ - β₁Xᵢ)²

对α₁求偏导并令其为0：
```
∂SSE₁/∂α₁ = -2Σ(Yᵢ - α₁ - β₁Xᵢ) = 0
```

得到：
```
Σ(Yᵢ - α₁ - β₁Xᵢ) = 0
nȲ - nα₁ - β₁nX̄ = 0
α₁ = Ȳ - β₁X̄
```

对β₁求偏导并令其为0：
```
∂SSE₁/∂β₁ = -2Σ(Yᵢ - α₁ - β₁Xᵢ)Xᵢ = 0
```

将α₁ = Ȳ - β₁X̄代入：
```
Σ(Yᵢ - Ȳ + β₁X̄ - β₁Xᵢ)Xᵢ = 0
Σ(Yᵢ - Ȳ)Xᵢ - β₁Σ(Xᵢ - X̄)Xᵢ = 0
```

注意到：Σ(Xᵢ - X̄)Xᵢ = Σ(Xᵢ - X̄)(Xᵢ - X̄ + X̄) = Σ(Xᵢ - X̄)²

因此：
```
Σ(Yᵢ - Ȳ)Xᵢ = β₁Σ(Xᵢ - X̄)²
```

而：Σ(Yᵢ - Ȳ)Xᵢ = Σ(Yᵢ - Ȳ)(Xᵢ - X̄ + X̄) = Σ(Yᵢ - Ȳ)(Xᵢ - X̄)

所以：
```
β₁ = Σ(Yᵢ - Ȳ)(Xᵢ - X̄) / Σ(Xᵢ - X̄)² = Cov(X,Y) / Var(X)
```

#### 2. 回归系数β₂的计算

类似地，对于X关于Y的回归：X = α₂ + β₂Y + ε₂

通过最小二乘法可得：
```
β₂ = Σ(Xᵢ - X̄)(Yᵢ - Ȳ) / Σ(Yᵢ - Ȳ)² = Cov(X,Y) / Var(Y)
```

#### 3. 乘积β₁ × β₂的计算

```
β₁ × β₂ = [Cov(X,Y) / Var(X)] × [Cov(X,Y) / Var(Y)]
        = [Cov(X,Y)]² / [Var(X) × Var(Y)]
```

根据相关系数的定义：
```
ρ = Corr(X,Y) = Cov(X,Y) / √[Var(X) × Var(Y)]
```

因此：
```
[Cov(X,Y)]² = ρ² × Var(X) × Var(Y)
```

代入得：
```
β₁ × β₂ = [ρ² × Var(X) × Var(Y)] / [Var(X) × Var(Y)] = ρ²
```

### 详细数值验证

#### 步骤1：生成数据
```python
import numpy as np

np.random.seed(42)
n = 1000
X = np.random.normal(0, 2, n)  # 均值0，标准差2
Y = 1.5 * X + np.random.normal(0, 1, n)  # Y = 1.5X + 噪声
```

#### 步骤2：计算基本统计量
```python
# 样本均值
X_mean = np.mean(X)  # ≈ 0.0265
Y_mean = np.mean(Y)  # ≈ 0.0398

# 样本方差
Var_X = np.var(X, ddof=1)  # ≈ 3.9209
Var_Y = np.var(Y, ddof=1)  # ≈ 9.8025

# 协方差
Cov_XY = np.cov(X, Y, ddof=1)[0,1]  # ≈ 5.8814
```

#### 步骤3：计算回归系数
```python
# 方法1：直接公式计算
beta1_formula = Cov_XY / Var_X  # ≈ 1.5000
beta2_formula = Cov_XY / Var_Y  # ≈ 0.6000

# 方法2：矩阵计算验证
X_centered = X - X_mean
Y_centered = Y - Y_mean

beta1_matrix = np.sum(X_centered * Y_centered) / np.sum(X_centered**2)
beta2_matrix = np.sum(X_centered * Y_centered) / np.sum(Y_centered**2)

print(f"β₁ (公式): {beta1_formula:.6f}")
print(f"β₁ (矩阵): {beta1_matrix:.6f}")
print(f"β₂ (公式): {beta2_formula:.6f}")
print(f"β₂ (矩阵): {beta2_matrix:.6f}")
```

#### 步骤4：验证乘积等于ρ²
```python
# 计算相关系数
correlation = Cov_XY / np.sqrt(Var_X * Var_Y)  # ≈ 0.9487

# 计算乘积
product = beta1_formula * beta2_formula  # ≈ 0.9000
rho_squared = correlation**2  # ≈ 0.9000

print(f"β₁ × β₂ = {product:.6f}")
print(f"ρ² = {rho_squared:.6f}")
print(f"差异: {abs(product - rho_squared):.10f}")
```

### 理论证明的几何解释

#### 1. 向量投影视角

在n维空间中，将X和Y看作向量：
- X = (X₁, X₂, ..., Xₙ)
- Y = (Y₁, Y₂, ..., Yₙ)

回归系数β₁实际上是Y在X方向上的投影系数：
```
β₁ = (Y·X) / (X·X) = Σ(XᵢYᵢ) / Σ(Xᵢ²)
```

但需要注意的是中心化处理：
```
β₁ = (Y_c·X_c) / (X_c·X_c)
```
其中Y_c和X_c是中心化后的向量。

#### 2. 相关系数的几何意义

相关系数ρ等于两个标准化向量的内积：
```
ρ = (X_std·Y_std) / n
```

其中X_std和Y_std是标准化后的向量。

### 范围分析的严格证明

#### 定理：β₁ × β₂ ∈ [0, 1]

**证明：**

1. **非负性证明：**
   ```
   β₁ × β₂ = ρ² ≥ 0
   ```
   因为ρ²是平方，必然非负。

2. **上界证明：**
   由Cauchy-Schwarz不等式：
   ```
   |Cov(X,Y)| ≤ √[Var(X) × Var(Y)]
   ```
   
   因此：
   ```
   |ρ| = |Cov(X,Y)| / √[Var(X) × Var(Y)] ≤ 1
   ```
   
   所以：
   ```
   ρ² ≤ 1
   ```

3. **边界情况：**
   - 当ρ = 0时（X和Y线性无关），β₁ × β₂ = 0
   - 当|ρ| = 1时（X和Y完全线性相关），β₁ × β₂ = 1

### 实际意义

β₁ × β₂ = ρ²的值可以解释为：
- **0.81-1.00**：非常强的线性关系
- **0.64-0.80**：强线性关系  
- **0.36-0.63**：中等线性关系
- **0.16-0.35**：弱线性关系
- **0.00-0.15**：很弱或无线性关系

### 总结

通过详细的数学推导和计算步骤，我们严格证明了：

**β₁ × β₂ = ρ²**，其中ρ是X和Y的相关系数。

**取值范围：[0, 1]**

这个结果不仅在理论上优雅，在实际应用中也提供了一个直观的方法来评估两个变量间线性关系的强度。
