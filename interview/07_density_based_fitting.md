# 面试题7：不同密度区域的最佳拟合策略

## 题目
Given various random points with different densities at different areas, what is the best fitting strategy?
给定在不同区域具有不同密度的各种随机点，最佳的拟合策略是什么？
## 解答

### 问题分析

当数据在不同区域具有不同密度时，传统的全局拟合方法可能不适用，需要考虑：
1. **密度变化**：数据点在空间中分布不均
2. **局部特征**：不同区域可能有不同的模式
3. **噪声水平**：密度低的区域可能噪声更大
4. **拟合目标**：全局趋势 vs 局部细节

### 策略分类

#### 1. 加权拟合方法 (Weighted Fitting)

**基本思想**：根据局部密度给数据点分配权重

**密度估计**：
```python
import numpy as np
from sklearn.neighbors import KernelDensity
from scipy.spatial.distance import cdist

def estimate_local_density(X, bandwidth=1.0):
    """估计每个点的局部密度"""
    kde = KernelDensity(bandwidth=bandwidth, kernel='gaussian')
    kde.fit(X)
    log_density = kde.score_samples(X)
    return np.exp(log_density)

def knn_density(X, k=5):
    """基于k近邻的密度估计"""
    distances = cdist(X, X)
    # 排除自身，取k个最近邻
    knn_distances = np.sort(distances, axis=1)[:, 1:k+1]
    # 密度与k近邻距离的倒数成正比
    density = 1.0 / (np.mean(knn_distances, axis=1) + 1e-10)
    return density
```

**加权回归**：
```python
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt

def weighted_polynomial_fit(X, y, weights, degree=2):
    """加权多项式拟合"""
    from sklearn.preprocessing import PolynomialFeatures
    from sklearn.linear_model import LinearRegression
    
    # 创建多项式特征
    poly_features = PolynomialFeatures(degree=degree)
    X_poly = poly_features.fit_transform(X.reshape(-1, 1))
    
    # 加权线性回归
    model = LinearRegression()
    model.fit(X_poly, y, sample_weight=weights)
    
    return model, poly_features

# 示例应用
def demo_weighted_fitting():
    # 生成不同密度的数据
    np.random.seed(42)
    
    # 高密度区域
    X1 = np.random.normal(2, 0.5, 100)
    y1 = X1**2 + np.random.normal(0, 0.1, 100)
    
    # 低密度区域
    X2 = np.random.normal(8, 1, 20)
    y2 = X2**2 + np.random.normal(0, 0.5, 20)
    
    X = np.concatenate([X1, X2])
    y = np.concatenate([y1, y2])
    
    # 估计密度
    density = knn_density(X.reshape(-1, 1), k=5)
    weights = density / np.max(density)  # 归一化权重
    
    # 普通拟合 vs 加权拟合
    model_normal, poly_normal = weighted_polynomial_fit(X, y, np.ones_like(weights))
    model_weighted, poly_weighted = weighted_polynomial_fit(X, y, weights)
    
    # 可视化
    X_plot = np.linspace(X.min(), X.max(), 300)
    X_plot_poly = poly_normal.transform(X_plot.reshape(-1, 1))
    
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.scatter(X, y, c=weights, cmap='viridis', alpha=0.7)
    plt.plot(X_plot, model_normal.predict(X_plot_poly), 'r-', label='普通拟合')
    plt.colorbar(label='权重')
    plt.title('普通拟合')
    plt.legend()
    
    plt.subplot(1, 2, 2)
    plt.scatter(X, y, c=weights, cmap='viridis', alpha=0.7)
    plt.plot(X_plot, model_weighted.predict(X_plot_poly), 'g-', label='加权拟合')
    plt.colorbar(label='权重')
    plt.title('加权拟合')
    plt.legend()
    
    plt.tight_layout()
    plt.show()
```

#### 2. 局部拟合方法 (Local Fitting)

**LOESS/LOWESS (Locally Weighted Scatterplot Smoothing)**
```python
from scipy import interpolate
import statsmodels.api as sm

def loess_fitting(X, y, frac=0.3):
    """LOESS局部加权回归"""
    # 排序数据
    sorted_indices = np.argsort(X)
    X_sorted = X[sorted_indices]
    y_sorted = y[sorted_indices]
    
    # LOESS拟合
    lowess = sm.nonparametric.lowess
    smoothed = lowess(y_sorted, X_sorted, frac=frac)
    
    return smoothed[:, 0], smoothed[:, 1]

def local_polynomial_regression(X, y, bandwidth=1.0, degree=2):
    """局部多项式回归"""
    X_pred = np.linspace(X.min(), X.max(), 100)
    y_pred = []
    
    for x_point in X_pred:
        # 计算权重（高斯核）
        distances = np.abs(X - x_point)
        weights = np.exp(-(distances / bandwidth)**2)
        
        # 局部加权多项式拟合
        if np.sum(weights) > 0:
            weights = weights / np.sum(weights)
            
            # 创建多项式特征
            X_local = X.reshape(-1, 1)
            poly_features = PolynomialFeatures(degree=degree)
            X_poly = poly_features.fit_transform(X_local)
            
            # 加权回归
            model = LinearRegression()
            model.fit(X_poly, y, sample_weight=weights)
            
            # 预测
            x_point_poly = poly_features.transform([[x_point]])
            y_pred.append(model.predict(x_point_poly)[0])
        else:
            y_pred.append(np.mean(y))
    
    return X_pred, np.array(y_pred)
```

#### 3. 分段拟合方法 (Piecewise Fitting)

**自适应分段**：
```python
from sklearn.tree import DecisionTreeRegressor
from sklearn.cluster import KMeans

def adaptive_piecewise_fitting(X, y, min_samples_leaf=10):
    """基于决策树的自适应分段拟合"""
    
    # 使用决策树找到分割点
    tree = DecisionTreeRegressor(
        min_samples_leaf=min_samples_leaf,
        max_depth=5
    )
    tree.fit(X.reshape(-1, 1), y)
    
    # 获取叶子节点
    leaf_indices = tree.apply(X.reshape(-1, 1))
    unique_leaves = np.unique(leaf_indices)
    
    # 为每个分段拟合模型
    piecewise_models = {}
    for leaf in unique_leaves:
        mask = leaf_indices == leaf
        if np.sum(mask) > 3:  # 确保有足够的点
            X_segment = X[mask]
            y_segment = y[mask]
            
            # 局部线性拟合
            model = LinearRegression()
            model.fit(X_segment.reshape(-1, 1), y_segment)
            piecewise_models[leaf] = {
                'model': model,
                'range': (X_segment.min(), X_segment.max()),
                'points': np.sum(mask)
            }
    
    return tree, piecewise_models

def density_based_segmentation(X, y, n_clusters=3):
    """基于密度的分段策略"""
    
    # 估计局部密度
    density = knn_density(X.reshape(-1, 1))
    
    # 基于密度聚类
    features = np.column_stack([X, density])
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    clusters = kmeans.fit_predict(features)
    
    # 为每个聚类拟合模型
    cluster_models = {}
    for cluster_id in range(n_clusters):
        mask = clusters == cluster_id
        if np.sum(mask) > 3:
            X_cluster = X[mask]
            y_cluster = y[mask]
            
            # 根据聚类大小选择模型复杂度
            if np.sum(mask) > 20:
                degree = 2
            else:
                degree = 1
            
            poly_features = PolynomialFeatures(degree=degree)
            X_poly = poly_features.fit_transform(X_cluster.reshape(-1, 1))
            
            model = LinearRegression()
            model.fit(X_poly, y_cluster)
            
            cluster_models[cluster_id] = {
                'model': model,
                'poly_features': poly_features,
                'density': np.mean(density[mask]),
                'size': np.sum(mask)
            }
    
    return clusters, cluster_models
```

#### 4. 多尺度拟合方法 (Multi-scale Fitting)

**小波拟合**：
```python
import pywt

def wavelet_fitting(X, y, wavelet='db4', mode='symmetric'):
    """小波变换拟合"""
    
    # 排序数据
    sorted_indices = np.argsort(X)
    X_sorted = X[sorted_indices]
    y_sorted = y[sorted_indices]
    
    # 小波分解
    coeffs = pywt.wavedec(y_sorted, wavelet, mode=mode)
    
    # 阈值去噪（可选）
    threshold = 0.1 * np.max(np.abs(coeffs[0]))
    coeffs_thresh = [pywt.threshold(c, threshold, mode='soft') for c in coeffs]
    
    # 重构信号
    y_reconstructed = pywt.waverec(coeffs_thresh, wavelet, mode=mode)
    
    return X_sorted, y_reconstructed[:len(X_sorted)]

def gaussian_process_fitting(X, y):
    """高斯过程回归"""
    from sklearn.gaussian_process import GaussianProcessRegressor
    from sklearn.gaussian_process.kernels import RBF, WhiteKernel
    
    # 自适应核函数
    kernel = RBF(length_scale=1.0) + WhiteKernel(noise_level=0.1)
    
    gp = GaussianProcessRegressor(
        kernel=kernel,
        alpha=1e-6,
        normalize_y=True,
        n_restarts_optimizer=10
    )
    
    gp.fit(X.reshape(-1, 1), y)
    
    X_pred = np.linspace(X.min(), X.max(), 100)
    y_pred, y_std = gp.predict(X_pred.reshape(-1, 1), return_std=True)
    
    return X_pred, y_pred, y_std
```

### 综合策略选择

```python
def choose_fitting_strategy(X, y, density_variation_threshold=2.0):
    """根据数据特征选择最佳拟合策略"""
    
    # 分析数据特征
    n_points = len(X)
    density = knn_density(X.reshape(-1, 1))
    density_ratio = np.max(density) / np.min(density)
    
    # 计算数据的空间分布
    X_range = X.max() - X.min()
    local_variations = []
    
    # 分析局部变化
    for i in range(0, len(X)-10, 5):
        local_X = X[i:i+10]
        local_y = y[i:i+10]
        if len(local_X) > 3:
            local_std = np.std(local_y)
            local_variations.append(local_std)
    
    avg_local_variation = np.mean(local_variations) if local_variations else 0
    
    print(f"数据点数量: {n_points}")
    print(f"密度变化比: {density_ratio:.2f}")
    print(f"平均局部变化: {avg_local_variation:.3f}")
    
    # 策略选择逻辑
    if density_ratio < density_variation_threshold:
        if n_points < 50:
            return "简单多项式拟合"
        else:
            return "LOESS局部拟合"
    
    elif density_ratio < 5.0:
        if avg_local_variation > 1.0:
            return "加权多项式拟合"
        else:
            return "局部多项式回归"
    
    else:  # 密度变化很大
        if n_points > 100:
            return "分段拟合 + 高斯过程"
        else:
            return "密度聚类 + 分段拟合"

# 完整的拟合流程
def comprehensive_fitting(X, y):
    """综合拟合流程"""
    
    strategy = choose_fitting_strategy(X, y)
    print(f"推荐策略: {strategy}")
    
    results = {}
    
    # 执行不同策略
    if "LOESS" in strategy:
        X_pred, y_pred = loess_fitting(X, y)
        results['loess'] = (X_pred, y_pred)
    
    if "加权" in strategy:
        density = knn_density(X.reshape(-1, 1))
        weights = density / np.max(density)
        model, poly_features = weighted_polynomial_fit(X, y, weights)
        X_pred = np.linspace(X.min(), X.max(), 100)
        X_pred_poly = poly_features.transform(X_pred.reshape(-1, 1))
        y_pred = model.predict(X_pred_poly)
        results['weighted'] = (X_pred, y_pred)
    
    if "高斯过程" in strategy:
        X_pred, y_pred, y_std = gaussian_process_fitting(X, y)
        results['gaussian_process'] = (X_pred, y_pred, y_std)
    
    if "分段" in strategy:
        tree, piecewise_models = adaptive_piecewise_fitting(X, y)
        results['piecewise'] = (tree, piecewise_models)
    
    return results
```

### 评估指标

```python
def evaluate_fitting_quality(X, y, X_pred, y_pred):
    """评估拟合质量"""
    from sklearn.metrics import mean_squared_error, r2_score
    from scipy.interpolate import interp1d
    
    # 插值到原始数据点
    if len(X_pred) != len(X):
        f = interp1d(X_pred, y_pred, kind='linear', 
                    bounds_error=False, fill_value='extrapolate')
        y_pred_interp = f(X)
    else:
        y_pred_interp = y_pred
    
    # 计算指标
    mse = mean_squared_error(y, y_pred_interp)
    r2 = r2_score(y, y_pred_interp)
    
    # 局部拟合质量
    density = knn_density(X.reshape(-1, 1))
    high_density_mask = density > np.median(density)
    low_density_mask = ~high_density_mask
    
    mse_high = mean_squared_error(y[high_density_mask], y_pred_interp[high_density_mask])
    mse_low = mean_squared_error(y[low_density_mask], y_pred_interp[low_density_mask])
    
    return {
        'overall_mse': mse,
        'overall_r2': r2,
        'high_density_mse': mse_high,
        'low_density_mse': mse_low,
        'density_balance': mse_low / mse_high if mse_high > 0 else np.inf
    }
```

### 总结

| 策略 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| 加权拟合 | 中等密度变化 | 简单有效 | 权重选择主观 |
| 局部拟合 | 非线性关系 | 自适应性强 | 计算复杂 |
| 分段拟合 | 大密度差异 | 处理异质性 | 分割点选择 |
| 高斯过程 | 不确定性重要 | 提供置信区间 | 计算昂贵 |
| 多尺度方法 | 多层次结构 | 捕获多尺度特征 | 参数调优复杂 |

**最佳实践**：
1. 先分析数据的密度分布特征
2. 根据密度变化程度选择策略
3. 考虑计算复杂度和精度的权衡
4. 使用交叉验证评估不同方法
5. 结合领域知识调整参数
