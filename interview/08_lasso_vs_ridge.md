# 面试题8：<PERSON><PERSON> vs Ridge回归对比

## 题目
Lasso vs Ridge
Lasso与Ridge的区别
## 解答

### 基本概念

#### Ridge回归 (L2正则化)
**目标函数**：
```
min ||y - Xβ||² + λ||β||²₂
```
其中 ||β||²₂ = Σβᵢ² 是L2范数的平方

#### Lasso回归 (L1正则化)
**目标函数**：
```
min ||y - Xβ||² + λ||β||₁
```
其中 ||β||₁ = Σ|βᵢ| 是L1范数

### 核心差异对比

| 特征 | Ridge (L2) | Lasso (L1) |
|------|------------|------------|
| **正则化项** | Σβᵢ² | Σ\|βᵢ\| |
| **几何形状** | 圆形约束 | 菱形约束 |
| **系数收缩** | 连续收缩 | 稀疏化（置零） |
| **特征选择** | 不能 | 自动特征选择 |
| **解的唯一性** | 唯一解 | 可能多解 |
| **计算复杂度** | 闭式解 | 迭代求解 |

### 数学性质分析

#### 1. 几何解释

**Ridge约束区域**：
- L2球面：β₁² + β₂² ≤ t
- 圆形边界，系数不会完全为零

**Lasso约束区域**：
- L1菱形：|β₁| + |β₂| ≤ t
- 菱形顶点在坐标轴上，容易产生稀疏解

```python
import numpy as np
import matplotlib.pyplot as plt

def plot_regularization_geometry():
    """可视化正则化的几何解释"""
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Ridge (L2)
    theta = np.linspace(0, 2*np.pi, 100)
    t = 1.0
    x_ridge = t * np.cos(theta)
    y_ridge = t * np.sin(theta)
    
    ax1.plot(x_ridge, y_ridge, 'b-', linewidth=2, label='L2约束')
    ax1.set_xlim(-1.5, 1.5)
    ax1.set_ylim(-1.5, 1.5)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlabel('β₁')
    ax1.set_ylabel('β₂')
    ax1.set_title('Ridge回归 (L2正则化)')
    ax1.legend()
    
    # Lasso (L1)
    x_lasso = np.array([t, 0, -t, 0, t])
    y_lasso = np.array([0, t, 0, -t, 0])
    
    ax2.plot(x_lasso, y_lasso, 'r-', linewidth=2, label='L1约束')
    ax2.set_xlim(-1.5, 1.5)
    ax2.set_ylim(-1.5, 1.5)
    ax2.grid(True, alpha=0.3)
    ax2.set_xlabel('β₁')
    ax2.set_ylabel('β₂')
    ax2.set_title('Lasso回归 (L1正则化)')
    ax2.legend()
    
    plt.tight_layout()
    plt.show()

plot_regularization_geometry()
```

#### 2. 系数路径分析

```python
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.datasets import make_regression
import matplotlib.pyplot as plt

def compare_coefficient_paths():
    """比较系数路径"""
    
    # 生成数据
    X, y = make_regression(n_samples=100, n_features=10, 
                          noise=0.1, random_state=42)
    
    # 正则化参数范围
    alphas = np.logspace(-4, 2, 50)
    
    # 存储系数
    ridge_coefs = []
    lasso_coefs = []
    
    for alpha in alphas:
        # Ridge
        ridge = Ridge(alpha=alpha)
        ridge.fit(X, y)
        ridge_coefs.append(ridge.coef_)
        
        # Lasso
        lasso = Lasso(alpha=alpha, max_iter=10000)
        lasso.fit(X, y)
        lasso_coefs.append(lasso.coef_)
    
    ridge_coefs = np.array(ridge_coefs)
    lasso_coefs = np.array(lasso_coefs)
    
    # 绘制系数路径
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Ridge系数路径
    for i in range(X.shape[1]):
        ax1.plot(alphas, ridge_coefs[:, i], label=f'特征{i+1}')
    ax1.set_xscale('log')
    ax1.set_xlabel('正则化参数 α')
    ax1.set_ylabel('系数值')
    ax1.set_title('Ridge回归系数路径')
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # Lasso系数路径
    for i in range(X.shape[1]):
        ax2.plot(alphas, lasso_coefs[:, i], label=f'特征{i+1}')
    ax2.set_xscale('log')
    ax2.set_xlabel('正则化参数 α')
    ax2.set_ylabel('系数值')
    ax2.set_title('Lasso回归系数路径')
    ax2.grid(True, alpha=0.3)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()
    plt.show()
    
    return alphas, ridge_coefs, lasso_coefs

alphas, ridge_coefs, lasso_coefs = compare_coefficient_paths()
```

### 实际应用场景

#### 1. 特征选择需求

```python
def feature_selection_comparison():
    """特征选择能力比较"""
    
    # 生成稀疏数据（只有少数特征有用）
    np.random.seed(42)
    n_samples, n_features = 100, 20
    n_informative = 5
    
    X = np.random.randn(n_samples, n_features)
    true_coef = np.zeros(n_features)
    true_coef[:n_informative] = np.random.randn(n_informative)
    y = X @ true_coef + 0.1 * np.random.randn(n_samples)
    
    # 拟合模型
    ridge = Ridge(alpha=1.0)
    lasso = Lasso(alpha=0.1)
    
    ridge.fit(X, y)
    lasso.fit(X, y)
    
    # 比较结果
    print("真实系数（前10个）:")
    print(f"{true_coef[:10]}")
    print(f"\nRidge估计（前10个）:")
    print(f"{ridge.coef_[:10]}")
    print(f"\nLasso估计（前10个）:")
    print(f"{lasso.coef_[:10]}")
    
    # 特征选择效果
    ridge_selected = np.sum(np.abs(ridge.coef_) > 0.01)
    lasso_selected = np.sum(np.abs(lasso.coef_) > 0.01)
    
    print(f"\n特征选择结果:")
    print(f"真实有效特征数: {n_informative}")
    print(f"Ridge选择特征数: {ridge_selected}")
    print(f"Lasso选择特征数: {lasso_selected}")
    
    # 可视化
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    x_pos = np.arange(n_features)
    
    ax1.bar(x_pos, true_coef, alpha=0.7, label='真实系数')
    ax1.bar(x_pos, ridge.coef_, alpha=0.7, label='Ridge估计')
    ax1.set_title('Ridge回归系数比较')
    ax1.set_xlabel('特征索引')
    ax1.set_ylabel('系数值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    ax2.bar(x_pos, true_coef, alpha=0.7, label='真实系数')
    ax2.bar(x_pos, lasso.coef_, alpha=0.7, label='Lasso估计')
    ax2.set_title('Lasso回归系数比较')
    ax2.set_xlabel('特征索引')
    ax2.set_ylabel('系数值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

feature_selection_comparison()
```

#### 2. 多重共线性处理

```python
def multicollinearity_comparison():
    """多重共线性情况下的比较"""
    
    np.random.seed(42)
    n_samples = 100
    
    # 创建相关特征
    X1 = np.random.randn(n_samples)
    X2 = X1 + 0.1 * np.random.randn(n_samples)  # 与X1高度相关
    X3 = np.random.randn(n_samples)  # 独立特征
    
    X = np.column_stack([X1, X2, X3])
    y = 2*X1 + 3*X3 + 0.1*np.random.randn(n_samples)
    
    print("特征相关性矩阵:")
    correlation_matrix = np.corrcoef(X.T)
    print(correlation_matrix)
    
    # 不同正则化强度下的比较
    alphas = [0.01, 0.1, 1.0, 10.0]
    
    results = []
    for alpha in alphas:
        ridge = Ridge(alpha=alpha)
        lasso = Lasso(alpha=alpha)
        
        ridge.fit(X, y)
        lasso.fit(X, y)
        
        results.append({
            'alpha': alpha,
            'ridge_coef': ridge.coef_.copy(),
            'lasso_coef': lasso.coef_.copy()
        })
    
    # 可视化结果
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.ravel()
    
    for i, result in enumerate(results):
        ax = axes[i]
        
        x_pos = np.arange(3)
        width = 0.35
        
        ax.bar(x_pos - width/2, result['ridge_coef'], width, 
               label='Ridge', alpha=0.7)
        ax.bar(x_pos + width/2, result['lasso_coef'], width, 
               label='Lasso', alpha=0.7)
        
        ax.set_title(f'α = {result["alpha"]}')
        ax.set_xlabel('特征')
        ax.set_ylabel('系数值')
        ax.set_xticks(x_pos)
        ax.set_xticklabels(['X1', 'X2', 'X3'])
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    return results

multicollinearity_results = multicollinearity_comparison()
```

### 优缺点总结

#### Ridge回归优缺点

**优点**：
1. **数值稳定**：有闭式解，计算快速
2. **处理多重共线性**：收缩相关特征的系数
3. **连续性**：系数连续变化，稳定性好
4. **适用高维**：p > n时仍可求解

**缺点**：
1. **无特征选择**：不能自动剔除无关特征
2. **解释性差**：保留所有特征，模型复杂
3. **存储需求**：需要存储所有特征

#### Lasso回归优缺点

**优点**：
1. **自动特征选择**：产生稀疏解
2. **模型简化**：提高解释性
3. **降低过拟合**：通过特征选择减少复杂度

**缺点**：
1. **选择任意性**：相关特征中随机选择一个
2. **不稳定**：小的数据变化可能导致不同的特征选择
3. **计算复杂**：需要迭代算法求解

### Elastic Net：两者结合

```python
def elastic_net_comparison():
    """Elastic Net作为Ridge和Lasso的结合"""
    
    # 生成数据
    X, y = make_regression(n_samples=100, n_features=20, 
                          n_informative=5, noise=0.1, random_state=42)
    
    # 不同的l1_ratio值
    l1_ratios = [0.0, 0.25, 0.5, 0.75, 1.0]  # 0=Ridge, 1=Lasso
    alpha = 0.1
    
    models = {}
    for l1_ratio in l1_ratios:
        if l1_ratio == 0.0:
            model = Ridge(alpha=alpha)
            name = 'Ridge'
        elif l1_ratio == 1.0:
            model = Lasso(alpha=alpha)
            name = 'Lasso'
        else:
            model = ElasticNet(alpha=alpha, l1_ratio=l1_ratio)
            name = f'ElasticNet(l1_ratio={l1_ratio})'
        
        model.fit(X, y)
        models[name] = model
    
    # 比较特征选择效果
    print("特征选择比较:")
    for name, model in models.items():
        n_selected = np.sum(np.abs(model.coef_) > 0.01)
        print(f"{name}: {n_selected}个特征")
    
    # 可视化系数
    fig, ax = plt.subplots(figsize=(12, 6))
    
    x_pos = np.arange(len(models))
    width = 0.15
    
    for i in range(X.shape[1]):
        coefs = [model.coef_[i] for model in models.values()]
        ax.bar(x_pos + i*width, coefs, width, 
               label=f'特征{i+1}', alpha=0.7)
    
    ax.set_xlabel('模型')
    ax.set_ylabel('系数值')
    ax.set_title('不同正则化方法的系数比较')
    ax.set_xticks(x_pos + width * X.shape[1] / 2)
    ax.set_xticklabels(models.keys(), rotation=45)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

elastic_net_comparison()
```

### 选择指南

```python
def choose_regularization_method(X, y, problem_type):
    """正则化方法选择指南"""
    
    n_samples, n_features = X.shape
    
    print(f"数据维度: {n_samples} × {n_features}")
    print(f"问题类型: {problem_type}")
    
    # 分析数据特征
    if n_features > 1:
        correlation_matrix = np.corrcoef(X.T)
        max_correlation = np.max(np.abs(correlation_matrix - np.eye(n_features)))
        print(f"最大特征相关性: {max_correlation:.3f}")
    else:
        max_correlation = 0
    
    # 给出建议
    recommendations = []
    
    if problem_type == "prediction":
        if n_features > n_samples:
            recommendations.append("Ridge: 处理高维数据")
        if max_correlation > 0.8:
            recommendations.append("Ridge: 处理多重共线性")
    
    elif problem_type == "interpretation":
        recommendations.append("Lasso: 自动特征选择")
        if max_correlation > 0.8:
            recommendations.append("Elastic Net: 平衡特征选择和稳定性")
    
    elif problem_type == "both":
        recommendations.append("Elastic Net: 兼顾预测和解释")
    
    print("\n推荐方法:")
    for rec in recommendations:
        print(f"- {rec}")
    
    return recommendations
```

### 总结

| 使用场景 | 推荐方法 | 理由 |
|----------|----------|------|
| 预测为主，特征多 | Ridge | 数值稳定，处理多重共线性 |
| 特征选择重要 | Lasso | 自动稀疏化，提高解释性 |
| 相关特征组 | Elastic Net | 倾向于选择整组相关特征 |
| 高维数据 (p>>n) | Ridge | 有唯一解，计算稳定 |
| 解释性重要 | Lasso | 产生简洁模型 |
| 预测+解释 | Elastic Net | 平衡两者需求 |

**关键要点**：
1. Ridge适合预测，Lasso适合特征选择
2. Elastic Net结合两者优点
3. 选择依赖于具体应用场景和数据特征
4. 交叉验证选择最优正则化参数
