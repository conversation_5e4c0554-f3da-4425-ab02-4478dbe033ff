# 面试题2：重复数据对回归结果的影响

## 题目
Given a Linear Regression, repeat the X and Y, how does it affect the beta, R^2, and p value?
给定一个线性回归，如果将X和Y数据重复一遍，这会如何影响回归系数beta、R²和p值？
## 解答

### 场景描述

假设原始数据集为：(x₁,y₁), (x₂,y₂), ..., (xₙ,yₙ)

重复数据后变为：(x₁,y₁), (x₁,y₁), (x₂,y₂), (x₂,y₂), ..., (xₙ,yₙ), (xₙ,yₙ)

即每个数据点都重复一次，样本量从n变为2n。

### 对各统计量的影响

#### 1. 回归系数 β (Beta)

**结论：β保持不变**

**原理：**
- β = Σ(xᵢ-x̄)(yᵢ-ȳ) / Σ(xᵢ-x̄)²
- 重复数据不改变样本均值：x̄和ȳ保持不变
- 协方差和方差都按相同比例增加（乘以2）
- 因此β = 2×Cov(X,Y) / 2×Var(X) = Cov(X,Y) / Var(X) 保持不变

#### 2. 决定系数 R² 

**结论：R²保持不变**

**原理：**
- R² = 1 - SSE/SST
- SSE (误差平方和) 和 SST (总平方和) 都按相同比例增加
- 因此比值保持不变
- 或者：R² = Corr(X,Y)²，相关系数不受重复数据影响

#### 3. p值 (P-value)

**结论：p值显著降低（更显著）**

**原理：**
- t统计量 = β / SE(β)
- SE(β) = σ/√Σ(xᵢ-x̄)² = σ/√(n-1)×s²ₓ
- 重复数据后：SE(β) = σ/√(2n-1)×s²ₓ ≈ σ/√(2n×s²ₓ) = SE(β)原始/√2
- t统计量增加√2倍
- 自由度从(n-2)增加到(2n-2)
- 更大的t统计量和更多自由度都导致p值降低

### 数学证明

#### Beta系数不变性证明

设原始数据的回归系数为：
β₀ = Σᵢ(xᵢ-x̄)(yᵢ-ȳ) / Σᵢ(xᵢ-x̄)²

重复数据后：
β₁ = Σᵢ[2×(xᵢ-x̄)(yᵢ-ȳ)] / Σᵢ[2×(xᵢ-x̄)²] = 2×Σᵢ(xᵢ-x̄)(yᵢ-ȳ) / 2×Σᵢ(xᵢ-x̄)² = β₀

#### 标准误差变化证明

原始标准误差：SE₀ = σ/√Σᵢ(xᵢ-x̄)²

重复后标准误差：SE₁ = σ/√Σᵢ[2×(xᵢ-x̄)²] = σ/√(2×Σᵢ(xᵢ-x̄)²) = SE₀/√2

### Python验证示例

```python
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.linear_model import LinearRegression

# 生成原始数据
np.random.seed(42)
n = 100
x_original = np.random.normal(0, 1, n)
y_original = 2 * x_original + np.random.normal(0, 0.5, n)

# 重复数据
x_repeated = np.tile(x_original, 2)
y_repeated = np.tile(y_original, 2)

# 原始回归
slope_orig, intercept_orig, r_orig, p_orig, se_orig = stats.linregress(x_original, y_original)

# 重复数据回归
slope_rep, intercept_rep, r_rep, p_rep, se_rep = stats.linregress(x_repeated, y_repeated)

print("原始数据结果:")
print(f"Beta: {slope_orig:.6f}")
print(f"R²: {r_orig**2:.6f}")
print(f"p-value: {p_orig:.6f}")
print(f"标准误差: {se_orig:.6f}")

print("\n重复数据结果:")
print(f"Beta: {slope_rep:.6f}")
print(f"R²: {r_rep**2:.6f}")
print(f"p-value: {p_rep:.6f}")
print(f"标准误差: {se_rep:.6f}")

print(f"\n变化比较:")
print(f"Beta变化: {slope_rep/slope_orig:.6f}")
print(f"R²变化: {(r_rep**2)/(r_orig**2):.6f}")
print(f"p值变化: {p_rep/p_orig:.6f}")
print(f"标准误差变化: {se_rep/se_orig:.6f} (理论值: {1/np.sqrt(2):.6f})")
```

### 实际意义

1. **数据质量问题**：重复数据不会改善模型的解释能力，但会人为降低p值
2. **统计显著性误判**：可能导致错误地认为关系更显著
3. **过度自信**：标准误差的减小可能给出虚假的精确度感觉
4. **数据清洗重要性**：强调了去重的重要性

### 总结

| 统计量 | 变化 | 原因 |
|--------|------|------|
| β (回归系数) | 不变 | 协方差和方差按相同比例变化 |
| R² | 不变 | 相关系数不变 |
| p值 | 显著降低 | 标准误差减小，自由度增加 |
| 标准误差 | 减小1/√2 | 样本量增加 |

**关键启示**：重复数据会产生虚假的统计显著性，这是数据预处理中需要特别注意的问题。
