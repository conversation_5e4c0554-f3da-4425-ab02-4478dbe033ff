# 面试题3：R²的符号性质

## 题目
Is R^2 always positive in linear regression? What if there is no intercept?
线性回归中R²总是正数吗？如果没有截距项会怎样？

## 解答

### 标准线性回归中的R²

#### 有截距项的情况

**结论：R²总是非负的 (R² ≥ 0)**

**定义：**
R² = 1 - SSE/SST

其中：
- SSE = Σ(yᵢ - ŷᵢ)² (残差平方和)
- SST = Σ(yᵢ - ȳ)² (总平方和)
- ŷᵢ = β₀ + β₁xᵢ (预测值)

**证明非负性：**
1. SSE ≥ 0 (平方和总是非负)
2. SST ≥ 0 (平方和总是非负)
3. 在有截距的OLS回归中，SSE ≤ SST (回归模型至少不比均值模型差)
4. 因此：0 ≤ SSE/SST ≤ 1
5. 所以：0 ≤ R² ≤ 1

### 无截距项回归中的R²

#### 问题的复杂性

**结论：R²可能为负值**

当回归模型不包含截距项时：Y = β₁X + ε

#### 两种R²定义

**定义1（传统定义）：**
R² = 1 - SSE/SST
- SSE = Σ(yᵢ - β₁xᵢ)²
- SST = Σ(yᵢ - ȳ)² (仍然使用样本均值)

**定义2（修正定义）：**
R² = 1 - SSE/TSS
- TSS = Σyᵢ² (总平方和，不减去均值)

#### 为什么传统R²可能为负

在无截距回归中，模型被强制通过原点，这可能导致：
1. 如果数据的真实关系不通过原点，强制约束会增加误差
2. SSE可能大于SST，导致R² < 0
3. 负的R²表明模型比简单的均值模型表现更差

### 数学示例

考虑数据点：(1,10), (2,11), (3,12)
- ȳ = 11
- 真实关系：y = 9 + x

**有截距回归：** y = 9 + x
- SSE = 0
- SST = (10-11)² + (11-11)² + (12-11)² = 2
- R² = 1 - 0/2 = 1

**无截距回归：** y = 5.5x (强制通过原点)
- 预测值：5.5, 11, 16.5
- SSE = (10-5.5)² + (11-11)² + (12-16.5)² = 20.25 + 0 + 20.25 = 40.5
- SST = 2 (同上)
- R² = 1 - 40.5/2 = -19.25 (负值！)

### Python验证示例

```python
import numpy as np
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score

# 生成不通过原点的数据
np.random.seed(42)
x = np.array([1, 2, 3, 4, 5]).reshape(-1, 1)
y = 10 + 2*x.flatten() + np.random.normal(0, 0.1, 5)  # y = 10 + 2x + noise

print("数据点:")
for i in range(len(x)):
    print(f"({x[i,0]}, {y[i]:.2f})")

# 有截距回归
model_with_intercept = LinearRegression(fit_intercept=True)
model_with_intercept.fit(x, y)
y_pred_with = model_with_intercept.predict(x)
r2_with = r2_score(y, y_pred_with)

# 无截距回归
model_no_intercept = LinearRegression(fit_intercept=False)
model_no_intercept.fit(x, y)
y_pred_no = model_no_intercept.predict(x)

# 计算无截距回归的R²（传统定义）
sse_no = np.sum((y - y_pred_no)**2)
sst = np.sum((y - np.mean(y))**2)
r2_no_traditional = 1 - sse_no/sst

# 计算无截距回归的R²（修正定义）
tss = np.sum(y**2)
r2_no_corrected = 1 - sse_no/tss

print(f"\n有截距回归:")
print(f"系数: {model_with_intercept.coef_[0]:.3f}")
print(f"截距: {model_with_intercept.intercept_:.3f}")
print(f"R²: {r2_with:.3f}")

print(f"\n无截距回归:")
print(f"系数: {model_no_intercept.coef_[0]:.3f}")
print(f"R²(传统定义): {r2_no_traditional:.3f}")
print(f"R²(修正定义): {r2_no_corrected:.3f}")

# 可视化
plt.figure(figsize=(10, 6))
plt.scatter(x, y, color='blue', label='数据点', s=50)
plt.plot(x, y_pred_with, 'r-', label=f'有截距: y={model_with_intercept.coef_[0]:.2f}x+{model_with_intercept.intercept_:.2f}')
plt.plot(x, y_pred_no, 'g--', label=f'无截距: y={model_no_intercept.coef_[0]:.2f}x')
plt.axhline(y=np.mean(y), color='orange', linestyle=':', label=f'均值: y={np.mean(y):.2f}')
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.title('有截距 vs 无截距回归比较')
plt.grid(True, alpha=0.3)
plt.show()
```

### 实际应用中的考虑

#### 何时使用无截距回归
1. **理论依据**：当理论上确定关系通过原点时
2. **物理定律**：如胡克定律 F = kx
3. **比例关系**：如价格与数量的线性关系

#### 注意事项
1. **R²解释**：无截距回归的R²需要谨慎解释
2. **模型比较**：不能直接比较有截距和无截距模型的R²
3. **诊断检查**：应该检查残差图确认模型适当性

### 软件实现差异

不同统计软件对无截距回归的R²计算可能不同：
- **R语言**：使用修正定义，确保R² ≥ 0
- **Python sklearn**：使用传统定义，可能出现负值
- **SAS/SPSS**：通常提供多种R²定义

### 总结

| 回归类型 | R²范围 | 原因 |
|----------|--------|------|
| 有截距 | [0, 1] | OLS保证SSE ≤ SST |
| 无截距 | (-∞, 1] | 强制约束可能增加误差 |

**关键要点：**
1. 标准线性回归（有截距）的R²总是非负
2. 无截距回归的R²可能为负，表明模型不适当
3. 负R²是模型选择错误的信号，应重新考虑模型设定
4. 在实际应用中，需要根据理论基础决定是否包含截距项
