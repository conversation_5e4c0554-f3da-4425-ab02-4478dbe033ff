import numpy as np


x1 = [0] * 20
x2 = [0] * 20

x1[19] = 1
x2[18] = 1
x2[19] = 1

print(x1)
print(x2)

np.mean(x1)

np.mean(x2)

np.std(x1)

np.std(x2)

import matplotlib.pyplot as plt

line1 = np.array([np.mean(x1), np.mean(x2)])
line1 = line1 - line1[0]
line2 = np.array([np.std(x1), np.std(x2)])
line2 = line2 - line2[0]

plt.plot(line1, label='mean')
plt.plot(line2, label='std')
plt.legend()
plt.show()