import numpy as np


x1 = [0] * 20
x2 = [0] * 20

x1[19] = 1
x2[18] = 1
x2[19] = 1

print(x1)
print(x2)

np.mean(x1)

np.mean(x2)

np.std(x1)

np.std(x2)

import matplotlib.pyplot as plt

line1 = np.array([np.mean(x1), np.mean(x2)])
line1 = line1 - line1[0]
line2 = np.array([np.std(x1), np.std(x2)])
line2 = line2 - line2[0]

plt.plot(line1, label='mean')
plt.plot(line2, label='std')
plt.legend()
plt.show()

import numpy as np
import matplotlib.pyplot as plt

def demo(i):
    x1 = [1] * (i-1) + [0] * (20 - i + 1)
    x2 = [1] * i + [0] * (20 - i)
    
    line1 = np.array([np.mean(x1), np.mean(x2)])
    line1 = line1 - line1[0]
    line2 = np.array([np.std(x1), np.std(x2)])
    line2 = line2 - line2[0]

    plt.plot(line1, label='mean')
    plt.plot(line2, label='std')
    plt.legend()
    plt.show()


demo(3)

demo(4)

demo(11)

import matplotlib.pyplot as plt
import numpy as np

def visualize_equivalence():
    """可视化等价条件"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 例子1：4个点在同一半圆内
    angles1 = [0.2, 0.8, 1.5, 2.8]  # 弧度
    
    # 计算间隙
    angles1_sorted = sorted(angles1)
    gaps1 = []
    for i in range(len(angles1_sorted)-1):
        gaps1.append(angles1_sorted[i+1] - angles1_sorted[i])
    gaps1.append(2*np.pi - angles1_sorted[-1] + angles1_sorted[0])
    
    # 绘制圆和点
    theta = np.linspace(0, 2*np.pi, 1000)
    axes[0,0].plot(np.cos(theta), np.sin(theta), 'k-', linewidth=2)
    
    for i, angle in enumerate(angles1):
        x, y = np.cos(angle), np.sin(angle)
        axes[0,0].plot(x, y, 'ro', markersize=10)
        axes[0,0].text(x*1.15, y*1.15, f'P{i+1}', fontsize=12, ha='center')
    
    # 标记最大间隙
    max_gap_idx = np.argmax(gaps1)
    if max_gap_idx < len(angles1_sorted)-1:
        start_angle = angles1_sorted[max_gap_idx]
        end_angle = angles1_sorted[max_gap_idx + 1]
    else:
        start_angle = angles1_sorted[-1]
        end_angle = angles1_sorted[0] + 2*np.pi
    
    # 绘制最大间隙
    gap_angles = np.linspace(start_angle, end_angle, 100)
    gap_x = 0.8 * np.cos(gap_angles)
    gap_y = 0.8 * np.sin(gap_angles)
    axes[0,0].plot(gap_x, gap_y, 'b-', linewidth=8, alpha=0.7, label=f'最大间隙: {max(gaps1):.2f}')
    
    # 绘制包含所有点的半圆
    semicircle_start = end_angle
    semicircle_end = start_angle + 2*np.pi if start_angle < end_angle else start_angle
    if semicircle_end - semicircle_start > np.pi:
        semicircle_start, semicircle_end = start_angle, end_angle + 2*np.pi
    
    semi_angles = np.linspace(semicircle_start, semicircle_start + np.pi, 100)
    semi_x = 1.1 * np.cos(semi_angles)
    semi_y = 1.1 * np.sin(semi_angles)
    axes[0,0].fill_between(semi_x, semi_y, alpha=0.3, color='green', label='包含所有点的半圆')
    
    axes[0,0].set_xlim(-1.5, 1.5)
    axes[0,0].set_ylim(-1.5, 1.5)
    axes[0,0].set_aspect('equal')
    axes[0,0].set_title(f'例子1: 所有点在同一半圆\n最大间隙={max(gaps1):.2f} > π={np.pi:.2f}')
    axes[0,0].legend()
    axes[0,0].grid(True, alpha=0.3)
    
    # 例子2：点分散在整个圆周
    angles2 = [0.5, 1.8, 3.2, 4.7, 5.9]
    
    angles2_sorted = sorted(angles2)
    gaps2 = []
    for i in range(len(angles2_sorted)-1):
        gaps2.append(angles2_sorted[i+1] - angles2_sorted[i])
    gaps2.append(2*np.pi - angles2_sorted[-1] + angles2_sorted[0])
    
    axes[0,1].plot(np.cos(theta), np.sin(theta), 'k-', linewidth=2)
    
    for i, angle in enumerate(angles2):
        x, y = np.cos(angle), np.sin(angle)
        axes[0,1].plot(x, y, 'ro', markersize=10)
        axes[0,1].text(x*1.15, y*1.15, f'P{i+1}', fontsize=12, ha='center')
    
    axes[0,1].set_xlim(-1.5, 1.5)
    axes[0,1].set_ylim(-1.5, 1.5)
    axes[0,1].set_aspect('equal')
    axes[0,1].set_title(f'例子2: 点分散分布\n最大间隙={max(gaps2):.2f} < π={np.pi:.2f}')
    axes[0,1].grid(True, alpha=0.3)
    
    # 间隙分析图
    gap_indices = range(1, len(gaps1)+1)
    axes[0,2].bar([i-0.2 for i in gap_indices], gaps1, width=0.4, label='例子1间隙', alpha=0.7)
    axes[0,2].bar([i+0.2 for i in range(1, len(gaps2)+1)], gaps2, width=0.4, label='例子2间隙', alpha=0.7)
    axes[0,2].axhline(np.pi, color='red', linestyle='--', linewidth=2, label='π阈值')
    axes[0,2].set_xlabel('间隙编号')
    axes[0,2].set_ylabel('间隙大小（弧度）')
    axes[0,2].set_title('间隙大小比较')
    axes[0,2].legend()
    axes[0,2].grid(True, alpha=0.3)
    
    # 理论解释
    axes[1,0].text(0.1, 0.9, '等价条件解释', fontsize=16, fontweight='bold')
    axes[1,0].text(0.1, 0.8, '正向：所有点在半圆内 ⟹ 存在大间隙', fontsize=12, color='blue')
    axes[1,0].text(0.15, 0.75, '• 如果所有点挤在半圆内', fontsize=10)
    axes[1,0].text(0.15, 0.7, '• 另一半圆必然是空的', fontsize=10)
    axes[1,0].text(0.15, 0.65, '• 空的半圆对应大间隙≥π', fontsize=10)
    
    axes[1,0].text(0.1, 0.55, '反向：存在大间隙 ⟹ 所有点在半圆内', fontsize=12, color='green')
    axes[1,0].text(0.15, 0.5, '• 如果有间隙≥π', fontsize=10)
    axes[1,0].text(0.15, 0.45, '• 剩余弧长≤π', fontsize=10)
    axes[1,0].text(0.15, 0.4, '• 所有点都在剩余弧上', fontsize=10)
    
    axes[1,0].text(0.1, 0.3, '关键洞察:', fontsize=14, fontweight='bold', color='red')
    axes[1,0].text(0.15, 0.25, '圆周总长度 = 2π', fontsize=12)
    axes[1,0].text(0.15, 0.2, '半圆长度 = π', fontsize=12)
    axes[1,0].text(0.15, 0.15, '大间隙 + 点分布区域 = 2π', fontsize=12)
    
    axes[1,0].set_xlim(0, 1)
    axes[1,0].set_ylim(0, 1)
    axes[1,0].axis('off')
    
    # 数学证明
    axes[1,1].text(0.1, 0.9, '数学证明', fontsize=16, fontweight='bold')
    axes[1,1].text(0.1, 0.8, '设n个点的角度为θ₁ ≤ θ₂ ≤ ... ≤ θₙ', fontsize=10)
    axes[1,1].text(0.1, 0.75, '间隙：G₁, G₂, ..., Gₙ', fontsize=10)
    axes[1,1].text(0.1, 0.7, '约束：∑Gᵢ = 2π', fontsize=10)
    
    axes[1,1].text(0.1, 0.6, '命题：max{Gᵢ} ≥ π ⟺ 所有点在半圆内', fontsize=10, fontweight='bold')
    
    axes[1,1].text(0.1, 0.5, '证明(⟹)：', fontsize=10, color='blue')
    axes[1,1].text(0.15, 0.45, '若max{Gᵢ} ≥ π，设Gⱼ ≥ π', fontsize=9)
    axes[1,1].text(0.15, 0.4, '则其余间隙之和 ≤ 2π - π = π', fontsize=9)
    axes[1,1].text(0.15, 0.35, '所有点分布在≤π的弧上', fontsize=9)
    
    axes[1,1].text(0.1, 0.25, '证明(⟸)：', fontsize=10, color='green')
    axes[1,1].text(0.15, 0.2, '若所有点在半圆[α,α+π]内', fontsize=9)
    axes[1,1].text(0.15, 0.15, '则[α+π,α+2π]内无点', fontsize=9)
    axes[1,1].text(0.15, 0.1, '对应间隙≥π', fontsize=9)
    
    axes[1,1].set_xlim(0, 1)
    axes[1,1].set_ylim(0, 1)
    axes[1,1].axis('off')
    
    # 极端情况分析
    axes[1,2].text(0.1, 0.9, '极端情况分析', fontsize=16, fontweight='bold')
    
    axes[1,2].text(0.1, 0.8, '情况1：所有点重合', fontsize=12, color='blue')
    axes[1,2].text(0.15, 0.75, '• 一个间隙 = 2π', fontsize=10)
    axes[1,2].text(0.15, 0.7, '• 显然 > π', fontsize=10)
    axes[1,2].text(0.15, 0.65, '• 所有点在任意半圆内', fontsize=10)
    
    axes[1,2].text(0.1, 0.55, '情况2：点均匀分布', fontsize=12, color='green')
    axes[1,2].text(0.15, 0.5, '• 每个间隙 = 2π/n', fontsize=10)
    axes[1,2].text(0.15, 0.45, '• 当n≥3时，2π/n < π', fontsize=10)
    axes[1,2].text(0.15, 0.4, '• 不存在大间隙', fontsize=10)
    axes[1,2].text(0.15, 0.35, '• 不能全在半圆内', fontsize=10)
    
    axes[1,2].text(0.1, 0.25, '临界情况：', fontsize=12, color='red')
    axes[1,2].text(0.15, 0.2, '• 最大间隙 = π', fontsize=10)
    axes[1,2].text(0.15, 0.15, '• 恰好在边界上', fontsize=10)
    
    axes[1,2].set_xlim(0, 1)
    axes[1,2].set_ylim(0, 1)
    axes[1,2].axis('off')
    
    plt.tight_layout()
    plt.show()

# 运行可视化
visualize_equivalence()

import quantstats as qs
import numpy as np
import pandas as pd

returns = pd.Series([0.01, -0.02, 0.03, -0.01, 0.02])
cumulative = qs.stats.compsum(returns)

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import quantstats as qs


np.random.seed(42)
days = 10
dates = pd.date_range(start="2023-01-01", periods=days)
daily_returns = np.random.normal(0.005, 0.01, days)


df = pd.DataFrame({
    "日收益": daily_returns
}, index=dates)


df["日收"] = (1 + df["日收益"]).cumprod()

mean_daily_return = df["日收益"].mean()
df["日收均值"] = (1 + mean_daily_return) **(np.arange(1, len(df)+1))

df["日收复合"] = qs.stats.compsum(df["日收益"]) + 1

expected_return = qs.stats.expected_return(df["日收益"])
geometric_return = qs.stats.geometric_mean(df["日收益"])

df["日收预期"] = (1 + expected_return)** (np.arange(1, len(df)+1))
df["日收几何"] = (1 + geometric_return) ** (np.arange(1, len(df) + 1))

# df[["累积净值_原始", "累积净值_均值", "累积净值_复合", "累积净值_预期"]].plot()
# 绘制图形
fig, ax = plt.subplots(figsize=(12, 6))

# 绘制四条曲线
ax.plot(df.index, df["日收"], label="简单收益累积", 
        marker="o", linestyle="-", color="blue")
ax.plot(df.index, df["日收均值"], label="按日均收益计算的累积净值", 
        marker="s", linestyle="--", color="green")
ax.plot(df.index, df["日收预期"], label="按预期收益计算的累积净值", 
        marker="^", linestyle="-.", color="red")
ax.plot(df.index, df["日收复合"], label="按复合收益计算的累积净值", 
        marker="*", linestyle=":", color="orange")


ax.set_title("四种累积净值对比", fontsize=15)
ax.set_xlabel("日期", fontsize=12)
ax.set_ylabel("累积净值", fontsize=12)
ax.grid(True, linestyle="--", alpha=0.7)
ax.legend(fontsize=10)

ax.xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
plt.xticks(rotation=45)

print("10日收益数据及三种累积净值计算结果：")
pd.set_option("display.float_format", "{:.4f}".format)
df

df["日收益"].plot()

(1 + 0.4/3)**3

import pandas as pd
import quantstats as qs
returns = pd.Series([0.1, 0.1, 0.1])
cumulative = qs.stats.compsum(returns)
cumulative

returns = pd.Series([0.1, 0.2, 0.1])
(returns + 1).cumprod() - 1

qs.stats.compsum(returns)

import numpy as np
# 生成示例每日收益率序列（1%、2%、-0.5%）
returns = np.array([0.01, 0.02, -0.005])

# 使用rebase函数计算复利净值
net_value = qs.rebase(returns)


daily_returns = [0.1, 0.2, 0.1]
sum(daily_returns) / len(daily_returns)

(1+0.4/3) ** 3

(1.452)**(1/3) - 1

(1 + 0.13237) ** 3

(1.3)**(1/30)-1

qs.stats.cagr
