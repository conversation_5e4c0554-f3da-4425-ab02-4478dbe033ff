import numpy as np


x1 = [0] * 20
x2 = [0] * 20

x1[19] = 1
x2[18] = 1
x2[19] = 1

print(x1)
print(x2)

np.mean(x1)

np.mean(x2)

np.std(x1)

np.std(x2)

import matplotlib.pyplot as plt

line1 = np.array([np.mean(x1), np.mean(x2)])
line1 = line1 - line1[0]
line2 = np.array([np.std(x1), np.std(x2)])
line2 = line2 - line2[0]

plt.plot(line1, label='mean')
plt.plot(line2, label='std')
plt.legend()
plt.show()

from manim import *
import numpy as np

class MeanStdAnimation(Scene):
    def construct(self):
        # 设置标题
        title = Text("序列中1的个数对均值和标准差的影响", font_size=36)
        title.to_edge(UP)
        self.play(Write(title))
        
        # 创建坐标轴
        axes = Axes(
            x_range=[1, 11, 1],
            y_range=[0, 0.6, 0.1],
            x_length=10,
            y_length=6,
            axis_config={"color": BLUE},
            x_axis_config={
                "numbers_to_include": np.arange(2, 11, 1),
                "numbers_with_elongated_ticks": np.arange(2, 11, 1),
            },
            y_axis_config={
                "numbers_to_include": np.arange(0, 0.7, 0.1),
                "numbers_with_elongated_ticks": np.arange(0, 0.7, 0.1),
            },
            tips=False,
        )
        
        # 坐标轴标签
        x_label = axes.get_x_axis_label("1的个数", direction=DOWN)
        y_label = axes.get_y_axis_label("数值", direction=LEFT)
        
        self.play(Create(axes), Write(x_label), Write(y_label))
        
        # 创建图例
        legend_mean = VGroup(
            Line(ORIGIN, RIGHT * 0.5, color=GREEN, stroke_width=4),
            Text("均值", font_size=24, color=GREEN)
        ).arrange(RIGHT, buff=0.1)
        
        legend_std = VGroup(
            Line(ORIGIN, RIGHT * 0.5, color=RED, stroke_width=4),
            Text("标准差", font_size=24, color=RED)
        ).arrange(RIGHT, buff=0.1)
        
        legend = VGroup(legend_mean, legend_std).arrange(DOWN, buff=0.2)
        legend.to_corner(UR, buff=0.5)
        self.play(Write(legend))
        
        # 存储数据点
        mean_points = []
        std_points = []
        mean_values = []
        std_values = []
        
        # 创建序列显示区域
        sequence_title = Text("当前序列:", font_size=24)
        sequence_title.to_edge(DOWN, buff=2)
        self.play(Write(sequence_title))
        
        # 动画演示i从2到10的过程
        for i in range(2, 11):
            # 创建序列（前i个位置为1，其余为0）
            sequence = [1] * i + [0] * (20 - i)
            
            # 计算均值和标准差
            mean_val = np.mean(sequence)
            std_val = np.std(sequence, ddof=0)  # 使用总体标准差
            
            mean_values.append(mean_val)
            std_values.append(std_val)
            
            # 显示当前序列（只显示前10个元素，用...表示其余）
            sequence_display = Text(
                f"[{', '.join(map(str, sequence[:10]))}, ...]", 
                font_size=20
            )
            sequence_display.next_to(sequence_title, DOWN)
            
            # 显示统计信息
            stats_text = Text(
                f"1的个数: {i}  均值: {mean_val:.3f}  标准差: {std_val:.3f}",
                font_size=20
            )
            stats_text.next_to(sequence_display, DOWN)
            
            if i == 2:
                self.play(Write(sequence_display), Write(stats_text))
            else:
                self.play(
                    Transform(sequence_display, sequence_display),
                    Transform(stats_text, stats_text)
                )
            
            # 在图上添加点
            mean_point = axes.coords_to_point(i, mean_val)
            std_point = axes.coords_to_point(i, std_val)
            
            mean_dot = Dot(mean_point, color=GREEN, radius=0.06)
            std_dot = Dot(std_point, color=RED, radius=0.06)
            
            self.play(Create(mean_dot), Create(std_dot))
            
            mean_points.append(mean_dot)
            std_points.append(std_dot)
            
            # 如果不是第一个点，画连线
            if i > 2:
                mean_line = Line(
                    axes.coords_to_point(i-1, mean_values[-2]),
                    axes.coords_to_point(i, mean_values[-1]),
                    color=GREEN,
                    stroke_width=3
                )
                std_line = Line(
                    axes.coords_to_point(i-1, std_values[-2]),
                    axes.coords_to_point(i, std_values[-1]),
                    color=RED,
                    stroke_width=3
                )
                self.play(Create(mean_line), Create(std_line))
            
            self.wait(0.5)
        
        # 计算并显示斜率
        self.wait(1)
        
        # 使用线性回归计算斜率
        x_data = np.array(range(2, 11))
        mean_slope = np.polyfit(x_data, mean_values, 1)[0]
        std_slope = np.polyfit(x_data, std_values, 1)[0]
        
        # 创建拟合直线
        mean_line_fit = axes.plot(
            lambda x: mean_slope * (x - 2) + mean_values[0],
            x_range=[2, 10],
            color=GREEN,
            stroke_width=2,
            stroke_opacity=0.7
        )
        
        std_line_fit = axes.plot(
            lambda x: std_slope * (x - 2) + std_values[0],
            x_range=[2, 10],
            color=RED,
            stroke_width=2,
            stroke_opacity=0.7
        )
        
        self.play(Create(mean_line_fit), Create(std_line_fit))
        
        # 显示斜率比较
        slope_comparison = VGroup(
            Text(f"均值斜率: {mean_slope:.4f}", font_size=24, color=GREEN),
            Text(f"标准差斜率: {std_slope:.4f}", font_size=24, color=RED),
            Text(f"标准差斜率 > 均值斜率: {std_slope > mean_slope}", 
                 font_size=24, color=YELLOW)
        ).arrange(DOWN, buff=0.2)
        
        slope_comparison.to_corner(UL, buff=0.5)
        self.play(Write(slope_comparison))
        
        # 添加结论
        conclusion = Text(
            "结论: 标准差增长速度确实比均值更快！",
            font_size=28,
            color=YELLOW
        )
        conclusion.to_edge(DOWN, buff=0.5)
        self.play(Write(conclusion))
        
        self.wait(3)

# 如果要运行这个动画，使用以下命令：
# manim -pql mean_std_animation.py MeanStdAnimation


!pip install manim

