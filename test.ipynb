import numpy as np


x1 = [0] * 20
x2 = [0] * 20

x1[19] = 1
x2[18] = 1
x2[19] = 1

print(x1)
print(x2)

np.mean(x1)

np.mean(x2)

np.std(x1)

np.std(x2)

import matplotlib.pyplot as plt

line1 = np.array([np.mean(x1), np.mean(x2)])
line1 = line1 - line1[0]
line2 = np.array([np.std(x1), np.std(x2)])
line2 = line2 - line2[0]

plt.plot(line1, label='mean')
plt.plot(line2, label='std')
plt.legend()
plt.show()

import numpy as np
import matplotlib.pyplot as plt

def demo(i):
    x1 = [1] * (i-1) + [0] * (20 - i + 1)
    x2 = [1] * i + [0] * (20 - i)
    
    line1 = np.array([np.mean(x1), np.mean(x2)])
    line1 = line1 - line1[0]
    line2 = np.array([np.std(x1), np.std(x2)])
    line2 = line2 - line2[0]

    plt.plot(line1, label='mean')
    plt.plot(line2, label='std')
    plt.legend()
    plt.show()


demo(3)

demo(4)

demo(5)