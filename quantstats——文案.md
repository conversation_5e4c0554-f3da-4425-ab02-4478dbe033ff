#下面这段话当提到Quantstats的时候就把图片呈现出来
作为老量化人，Quantstats这款用于交易策略绩效分析的开源 Python 库是啥地位咱都不用多说了吧？

咱平时做策略评估的年化收益率 (Annualized Return)、夏普比率 (Sharpe Ratio)，最大回撤 (Max Drawdown)，累积收益曲线等常见的图表都可以用它计算并绘制，5.8k 的 stars实至名归？

但是！！！如果我们在github上进入quantstats主页，点开issue页面你会发现issue堆满天。例如，在 Python 3.12 版本下（其他版本新安装 Quantstats 也可能出现），会遇到如下错误：
![alt text](image-3.png)
自己录制一段：import quantstats as qs回车报错的视频，
并配上“蹬！”报错的声音

这是一个只在 Jupyter Notebook 下出现的问题，主要由于 nbformat 升级导致。该问题修复后，你还可能遇到如下错误：
![alt text](image-4.png)

几个月来，尽管社区不断提交修复，但作者Ran Aroussi一直没有时间发布新版本。可能因为原版 Quantstats 没有单元测试和 CI，每次发布都需手动测试，极为繁琐，作者难以抽身。（哭泣的表情包）
![alt text](image.png)


别急！！为了满足我们匡醍的学员以及我的各位量化电子网友们的使用，考虑到 #416 等 bug 的紧迫性我们提前发布了： quantstats-reloaded 新包（放一个开心，跃跃欲试的表情包）

如果你也受到 Quantstats 问题影响，请使用 quantstats-reloaded。本版本除了修复 #416、#420 等 bug，还进行了如下重构：（清单，逐行出现）
