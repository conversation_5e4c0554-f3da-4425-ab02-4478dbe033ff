from manim import *
import numpy as np

class SimplePDFCDF(Scene):
    def construct(self):
        # 白色背景
        self.camera.background_color = WHITE
        
        # 标题
        title = Text("PDF与CDF关系演示", font_size=24, color=BLACK)
        title.to_edge(UP, buff=0.3)
        self.add(title)
        
        # 创建坐标系
        # CDF坐标系（上方）
        cdf_axes = Axes(
            x_range=[-2, 2, 1],
            y_range=[0, 1, 0.2],
            x_length=6,
            y_length=2,
            axis_config={"color": BLACK}
        ).shift(UP * 1.5)
        
        # PDF坐标系（下方）
        pdf_axes = Axes(
            x_range=[-2, 2, 1],
            y_range=[0, 0.5, 0.1],
            x_length=6,
            y_length=2,
            axis_config={"color": BLACK}
        ).shift(DOWN * 1.5)
        
        # 标签
        cdf_label = Text("CDF", font_size=16, color=RED).next_to(cdf_axes, LEFT)
        pdf_label = Text("PDF", font_size=16, color=BLUE).next_to(pdf_axes, LEFT)
        
        # 显示坐标系
        self.play(Create(cdf_axes), Create(pdf_axes), Write(cdf_label), Write(pdf_label))
        
        # 简单的PDF函数
        def simple_pdf(x):
            return 0.3 * np.exp(-x**2)
        
        # 对应的CDF（简化计算）
        def simple_cdf(x):
            # 简单的近似CDF
            return 0.5 * (1 + np.tanh(x))
        
        # 绘制PDF曲线
        pdf_curve = pdf_axes.plot(simple_pdf, x_range=[-2, 2], color=BLUE, stroke_width=2)
        self.play(Create(pdf_curve))
        
        # 创建追踪器
        x_tracker = ValueTracker(-2)
        
        # 积分区域（简化处理）
        integral_area = always_redraw(
            lambda: pdf_axes.get_area(
                pdf_curve,
                x_range=[-2, x_tracker.get_value()],
                color=BLUE,
                opacity=0.3
            ) if x_tracker.get_value() > -2 else VGroup()
        )
        
        # CDF点
        cdf_dot = always_redraw(
            lambda: Dot(
                cdf_axes.c2p(x_tracker.get_value(), simple_cdf(x_tracker.get_value())),
                color=RED,
                radius=0.05
            )
        )
        
        # CDF轨迹
        cdf_path = TracedPath(
            lambda: cdf_axes.c2p(x_tracker.get_value(), simple_cdf(x_tracker.get_value())),
            stroke_color=RED,
            stroke_width=2
        )
        
        # 垂直线
        vertical_line = always_redraw(
            lambda: Line(
                pdf_axes.c2p(x_tracker.get_value(), 0),
                pdf_axes.c2p(x_tracker.get_value(), simple_pdf(x_tracker.get_value())),
                color=GREEN,
                stroke_width=2
            )
        )
        
        # 添加动画元素
        self.add(integral_area, cdf_dot, cdf_path, vertical_line)
        
        # 执行动画
        self.play(
            x_tracker.animate.set_value(2),
            run_time=6,
            rate_func=linear
        )
        
        # 最终说明
        final_text = Text("积分完成！", font_size=16, color=GREEN)
        final_text.to_corner(DR)
        self.play(Write(final_text))
        
        self.wait(1)

if __name__ == "__main__":
    pass
