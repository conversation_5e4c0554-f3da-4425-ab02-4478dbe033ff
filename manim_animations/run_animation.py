#!/usr/bin/env python3
"""
运行PDF-CDF动画的脚本
使用方法：python run_animation.py
"""

import subprocess
import sys
import os

def check_manim():
    """检查Manim是否安装"""
    try:
        import manim
        print("✅ Manim已安装")
        return True
    except ImportError:
        print("❌ 请先安装Manim:")
        print("   pip install manim")
        print("   或者: conda install -c conda-forge manim")
        return False

def run_test_animation():
    """运行测试版动画"""
    if not check_manim():
        return

    script_path = "test_animation.py"
    if not os.path.exists(script_path):
        print(f"❌ 找不到文件: {script_path}")
        return

    print("🎬 开始渲染测试动画...")

    cmd = [
        "manim",
        "-pql",  # 预览，低质量，快速渲染
        "--fps", "15",  # 降低帧率加快渲染
        script_path,
        "TestPDFCDF"
    ]

    print(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 测试动画渲染成功！")
            print("📁 输出文件位置: media/videos/test_animation/")
        else:
            print("❌ 渲染失败:")
            print(result.stderr)

    except Exception as e:
        print(f"❌ 运行出错: {e}")

def run_full_animation():
    """运行完整版动画"""
    if not check_manim():
        return

    script_path = "pdf_cdf_animation.py"
    if not os.path.exists(script_path):
        print(f"❌ 找不到文件: {script_path}")
        return

    print("🎬 开始渲染完整动画...")

    cmd = [
        "manim",
        "-pql",  # 预览，低质量
        script_path,
        "SimplePDFCDFAnimation"
    ]

    print(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ 完整动画渲染成功！")
            print("📁 输出文件位置: media/videos/pdf_cdf_animation/")
        else:
            print("❌ 渲染失败:")
            print(result.stderr)

    except Exception as e:
        print(f"❌ 运行出错: {e}")

def run_high_quality():
    """运行高质量版本"""
    if not check_manim():
        return

    script_path = "pdf_cdf_animation.py"

    cmd = [
        "manim",
        "-pqh",  # 预览，高质量
        script_path,
        "SimplePDFCDFAnimation"
    ]

    print(f"🎬 渲染高质量版本...")
    print(f"执行命令: {' '.join(cmd)}")

    try:
        subprocess.run(cmd, check=True)
        print("✅ 高质量动画渲染成功！")
    except subprocess.CalledProcessError as e:
        print(f"❌ 渲染失败: {e}")

if __name__ == "__main__":
    print("PDF-CDF动画渲染器")
    print("=" * 40)
    print("1. 测试版动画 (快速，简单)")
    print("2. 完整版动画 (详细，美观)")
    print("3. 高质量版本 (慢，高清)")
    print("=" * 40)

    choice = input("请选择 (1/2/3): ").strip()

    if choice == "1":
        run_test_animation()
    elif choice == "2":
        run_full_animation()
    elif choice == "3":
        run_high_quality()
    else:
        print("默认运行测试版动画")
        run_test_animation()
