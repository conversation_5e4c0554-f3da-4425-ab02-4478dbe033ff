from manim import *
import numpy as np

class TestPDFCDF(Scene):
    def construct(self):
        # 白色背景
        self.camera.background_color = WHITE
        
        # 简单标题
        title = Text("PDF与CDF关系演示", font_size=28, color=BLACK)
        title.to_edge(UP, buff=0.5)
        self.add(title)
        
        # 创建坐标系
        # CDF坐标系（上方）
        cdf_axes = Axes(
            x_range=[-2, 2, 0.5],
            y_range=[0, 1, 0.2],
            x_length=6,
            y_length=2.5,
            axis_config={"color": BLACK, "stroke_width": 2}
        ).shift(UP * 1.5)
        
        # PDF坐标系（下方）
        pdf_axes = Axes(
            x_range=[-2, 2, 0.5],
            y_range=[0, 0.6, 0.1],
            x_length=6,
            y_length=2.5,
            axis_config={"color": BLACK, "stroke_width": 2}
        ).shift(DOWN * 1.5)
        
        # 标签
        cdf_label = Text("CDF: F(x)", font_size=18, color=DARK_BLUE)
        cdf_label.next_to(cdf_axes, LEFT, buff=0.3)
        
        pdf_label = Text("PDF: f(x)", font_size=18, color=BLUE)
        pdf_label.next_to(pdf_axes, LEFT, buff=0.3)
        
        # 显示坐标系
        self.play(
            Create(cdf_axes),
            Create(pdf_axes),
            Write(cdf_label),
            Write(pdf_label)
        )
        
        # 简单的正态分布PDF
        def simple_pdf(x):
            return 0.4 * np.exp(-x**2 / 2)
        
        # 对应的CDF（数值积分）
        def simple_cdf(x):
            if x <= -2:
                return 0
            elif x >= 2:
                return 1
            else:
                # 简单数值积分
                dx = 0.05
                x_vals = np.arange(-2, x + dx, dx)
                y_vals = [simple_pdf(xi) for xi in x_vals]
                integral = np.sum(y_vals) * dx
                return min(integral, 1)  # 确保不超过1
        
        # 绘制PDF曲线
        pdf_curve = pdf_axes.plot(
            simple_pdf,
            x_range=[-2, 2],
            color=BLUE,
            stroke_width=3
        )
        
        self.play(Create(pdf_curve))
        
        # 创建追踪器
        x_tracker = ValueTracker(-2)
        
        # 积分区域
        def get_integral_area():
            x_val = x_tracker.get_value()
            if x_val <= -2:
                return VGroup()  # 空的组
            return pdf_axes.get_area(
                pdf_curve,
                x_range=[-2, x_val],
                color=BLUE,
                opacity=0.3
            )
        
        integral_area = always_redraw(get_integral_area)
        
        # CDF点
        cdf_dot = always_redraw(
            lambda: Dot(
                cdf_axes.c2p(x_tracker.get_value(), simple_cdf(x_tracker.get_value())),
                color=RED,
                radius=0.06
            )
        )
        
        # CDF轨迹
        cdf_path = TracedPath(
            lambda: cdf_axes.c2p(x_tracker.get_value(), simple_cdf(x_tracker.get_value())),
            stroke_color=RED,
            stroke_width=3
        )
        
        # 垂直线
        vertical_line = always_redraw(
            lambda: Line(
                pdf_axes.c2p(x_tracker.get_value(), 0),
                pdf_axes.c2p(x_tracker.get_value(), simple_pdf(x_tracker.get_value())),
                color=GREEN,
                stroke_width=3
            )
        )
        
        # 数值显示
        value_display = always_redraw(
            lambda: VGroup(
                Text(f"x = {x_tracker.get_value():.1f}", font_size=14, color=BLACK),
                Text(f"F(x) = {simple_cdf(x_tracker.get_value()):.2f}", font_size=14, color=RED)
            ).arrange(DOWN, buff=0.1).to_corner(UR, buff=0.5)
        )
        
        # 添加动画元素
        self.add(integral_area, cdf_dot, cdf_path, vertical_line, value_display)
        
        # 执行动画
        self.play(
            x_tracker.animate.set_value(2),
            run_time=8,
            rate_func=linear
        )
        
        # 最终说明
        final_text = Text("积分完成，F(∞) ≈ 1", font_size=16, color=GREEN)
        final_text.to_corner(DL, buff=0.5)
        self.play(Write(final_text))
        
        self.wait(2)

if __name__ == "__main__":
    # 可以直接运行这个文件进行测试
    pass
