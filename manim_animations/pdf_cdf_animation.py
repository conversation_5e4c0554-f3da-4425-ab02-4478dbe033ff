from manim import *
import numpy as np

class PDFCDFAnimation(Scene):
    def construct(self):
        # 设置场景
        self.camera.background_color = WHITE
        
        # 创建标题
        title = Text("概率密度函数与累积分布函数的关系", font_size=36, color=BLACK)
        title.to_edge(UP, buff=0.5)
        self.add(title)
        
        # 创建上下两个坐标系
        # 上方：CDF
        cdf_axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[0, 1.2, 0.2],
            x_length=8,
            y_length=3,
            axis_config={"color": BLACK, "stroke_width": 2},
            tips=False
        ).shift(UP * 1.5)
        
        # 下方：PDF
        pdf_axes = Axes(
            x_range=[-4, 4, 1],
            y_range=[0, 0.5, 0.1],
            x_length=8,
            y_length=3,
            axis_config={"color": BLACK, "stroke_width": 2},
            tips=False
        ).shift(DOWN * 1.5)
        
        # 添加坐标轴标签
        cdf_label = Text("累积分布函数 F(x)", font_size=24, color=BLACK)
        cdf_label.next_to(cdf_axes, UP, buff=0.2)
        
        pdf_label = Text("概率密度函数 f(x)", font_size=24, color=BLACK)
        pdf_label.next_to(pdf_axes, DOWN, buff=0.2)
        
        # 添加坐标轴
        self.play(
            Create(cdf_axes),
            Create(pdf_axes),
            Write(cdf_label),
            Write(pdf_label)
        )
        
        # 定义正态分布函数
        def normal_pdf(x, mu=0, sigma=1):
            return (1 / (sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma) ** 2)
        
        def normal_cdf(x, mu=0, sigma=1):
            # 使用数值积分近似计算CDF
            from scipy.stats import norm
            return norm.cdf(x, mu, sigma)
        
        # 创建PDF曲线
        pdf_curve = pdf_axes.plot(
            lambda x: normal_pdf(x),
            x_range=[-4, 4],
            color=BLUE,
            stroke_width=3
        )
        
        # 创建CDF曲线（初始为空）
        cdf_curve = pdf_axes.plot(
            lambda x: 0,
            x_range=[-4, 4],
            color=RED,
            stroke_width=3
        )
        
        # 绘制PDF曲线
        self.play(Create(pdf_curve), run_time=2)
        
        # 创建移动的垂直线
        vertical_line = Line(
            start=pdf_axes.c2p(-4, 0),
            end=pdf_axes.c2p(-4, normal_pdf(-4)),
            color=GREEN,
            stroke_width=4
        )
        
        # 创建积分区域
        integral_area = pdf_axes.get_area(
            pdf_curve,
            x_range=[-4, -4],
            color=BLUE,
            opacity=0.3
        )
        
        # 创建CDF上的点
        cdf_point = Dot(
            cdf_axes.c2p(-4, normal_cdf(-4)),
            color=RED,
            radius=0.08
        )
        
        # 添加初始元素
        self.add(vertical_line, integral_area, cdf_point)
        
        # 创建CDF曲线的轨迹
        cdf_path = VMobject(color=RED, stroke_width=3)
        cdf_path.set_points_as_corners([cdf_axes.c2p(-4, normal_cdf(-4))])
        
        self.add(cdf_path)
        
        # 动画：从左到右移动
        x_values = np.linspace(-4, 4, 200)
        
        for i, x in enumerate(x_values[1:], 1):
            # 更新垂直线位置
            new_vertical_line = Line(
                start=pdf_axes.c2p(x, 0),
                end=pdf_axes.c2p(x, normal_pdf(x)),
                color=GREEN,
                stroke_width=4
            )
            
            # 更新积分区域
            new_integral_area = pdf_axes.get_area(
                pdf_curve,
                x_range=[-4, x],
                color=BLUE,
                opacity=0.3
            )
            
            # 更新CDF点
            new_cdf_point = Dot(
                cdf_axes.c2p(x, normal_cdf(x)),
                color=RED,
                radius=0.08
            )
            
            # 更新CDF路径
            new_point = cdf_axes.c2p(x, normal_cdf(x))
            if i == 1:
                cdf_path.add_line_to(new_point)
            else:
                cdf_path.add_line_to(new_point)
            
            # 执行动画
            if i % 5 == 0:  # 每5个点更新一次，加快动画速度
                self.play(
                    Transform(vertical_line, new_vertical_line),
                    Transform(integral_area, new_integral_area),
                    Transform(cdf_point, new_cdf_point),
                    run_time=0.1
                )
        
        # 添加说明文字
        explanation = VGroup(
            Text("积分区域面积 = CDF值", font_size=20, color=BLACK),
            Text("PDF积分 → CDF单调递增", font_size=20, color=BLACK)
        ).arrange(DOWN, buff=0.2)
        explanation.to_corner(DR, buff=0.5)
        
        self.play(Write(explanation))
        
        # 最终暂停
        self.wait(2)

class SimplePDFCDFAnimation(Scene):
    def construct(self):
        # 设置白色背景
        self.camera.background_color = WHITE

        # 创建标题
        title = Text("概率密度函数与累积分布函数的关系", font_size=32, color=BLACK, font="SimHei")
        title.to_edge(UP, buff=0.3)
        self.play(Write(title))

        # 创建上下两个坐标系
        # 上方：CDF (累积分布函数)
        axes_cdf = Axes(
            x_range=[-3, 3, 1],
            y_range=[0, 1.1, 0.2],
            x_length=8,
            y_length=2.8,
            axis_config={"color": BLACK, "stroke_width": 2},
            tips=False
        ).shift(UP * 1.2)

        # 下方：PDF (概率密度函数)
        axes_pdf = Axes(
            x_range=[-3, 3, 1],
            y_range=[0, 0.5, 0.1],
            x_length=8,
            y_length=2.8,
            axis_config={"color": BLACK, "stroke_width": 2},
            tips=False
        ).shift(DOWN * 1.2)

        # 添加坐标轴标签
        cdf_title = Text("累积分布函数 F(x) = P(X ≤ x)", font_size=20, color=DARK_BLUE)
        cdf_title.next_to(axes_cdf, UP, buff=0.1)

        pdf_title = Text("概率密度函数 f(x)", font_size=20, color=BLUE)
        pdf_title.next_to(axes_pdf, DOWN, buff=0.1)

        # 添加坐标轴和标签
        self.play(
            Create(axes_cdf),
            Create(axes_pdf),
            Write(cdf_title),
            Write(pdf_title),
            run_time=2
        )

        # 定义正态分布函数 (使用简化版本避免scipy依赖)
        def normal_pdf(x, mu=0, sigma=1):
            return (1 / (sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma) ** 2)

        # 使用数值积分计算CDF
        def normal_cdf_approx(x, mu=0, sigma=1):
            # 简单的数值积分
            if x <= -3:
                return 0
            elif x >= 3:
                return 1
            else:
                # 梯形法则数值积分
                dx = 0.01
                x_vals = np.arange(-3, x + dx, dx)
                y_vals = [normal_pdf(xi, mu, sigma) for xi in x_vals]
                return np.trapz(y_vals, x_vals)

        # 绘制PDF曲线
        pdf_graph = axes_pdf.plot(
            lambda x: normal_pdf(x),
            x_range=[-3, 3],
            color=BLUE,
            stroke_width=3
        )

        self.play(Create(pdf_graph), run_time=2)

        # 添加说明文字
        explanation = VGroup(
            Text("观察：随着积分区域扩大", font_size=16, color=BLACK),
            Text("CDF值单调递增至1", font_size=16, color=BLACK)
        ).arrange(DOWN, buff=0.1)
        explanation.to_corner(UR, buff=0.3)
        self.play(Write(explanation))

        # 动画积分过程
        x_tracker = ValueTracker(-3)

        # 积分区域 (阴影)
        integral_area = always_redraw(
            lambda: axes_pdf.get_area(
                pdf_graph,
                x_range=[-3, x_tracker.get_value()],
                color=LIGHT_BLUE,
                opacity=0.6
            )
        )

        # CDF上的移动点
        cdf_dot = always_redraw(
            lambda: Dot(
                axes_cdf.c2p(x_tracker.get_value(), normal_cdf_approx(x_tracker.get_value())),
                color=RED,
                radius=0.08
            )
        )

        # CDF轨迹
        cdf_trace = TracedPath(
            lambda: axes_cdf.c2p(x_tracker.get_value(), normal_cdf_approx(x_tracker.get_value())),
            stroke_color=RED,
            stroke_width=4
        )

        # 移动的垂直线
        vertical_line = always_redraw(
            lambda: Line(
                axes_pdf.c2p(x_tracker.get_value(), 0),
                axes_pdf.c2p(x_tracker.get_value(), normal_pdf(x_tracker.get_value())),
                color=GREEN,
                stroke_width=4
            )
        )

        # 当前x值显示
        x_value_text = always_redraw(
            lambda: Text(
                f"x = {x_tracker.get_value():.1f}",
                font_size=18,
                color=BLACK
            ).next_to(vertical_line, UP, buff=0.1)
        )

        # CDF值显示
        cdf_value_text = always_redraw(
            lambda: Text(
                f"F(x) = {normal_cdf_approx(x_tracker.get_value()):.3f}",
                font_size=18,
                color=RED
            ).next_to(cdf_dot, RIGHT, buff=0.1)
        )

        # 添加动画元素
        self.add(integral_area, cdf_dot, cdf_trace, vertical_line, x_value_text, cdf_value_text)

        # 执行主要动画：从左到右积分
        self.play(
            x_tracker.animate.set_value(3),
            run_time=10,
            rate_func=linear
        )

        # 添加最终说明
        final_explanation = VGroup(
            Text("积分完成！", font_size=20, color=GREEN, weight=BOLD),
            Text("F(+∞) = 1", font_size=18, color=RED),
            Text("这就是概率的归一化性质", font_size=16, color=BLACK)
        ).arrange(DOWN, buff=0.1)
        final_explanation.to_corner(DL, buff=0.3)

        self.play(Write(final_explanation))
        self.wait(3)

if __name__ == "__main__":
    # 运行简化版本
    scene = SimplePDFCDFAnimation()
    scene.render()
