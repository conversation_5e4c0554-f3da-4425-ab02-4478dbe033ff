# PDF-CDF动画教程

## 🎯 动画说明

这个动画展示了概率密度函数(PDF)和累积分布函数(CDF)之间的关系：

- **上方图形**：累积分布函数 F(x) = P(X ≤ x)
- **下方图形**：概率密度函数 f(x)
- **动画过程**：从左到右积分，展示CDF如何单调递增到1

## 🚀 快速开始

### 1. 安装依赖
```bash
# 安装Manim
pip install manim

# 如果需要中文字体支持
pip install matplotlib
```

### 2. 运行动画
```bash
# 方法1：直接运行（推荐）
python run_animation.py

# 方法2：使用manim命令
manim -pql pdf_cdf_animation.py SimplePDFCDFAnimation
```

### 3. 渲染选项
```bash
# 低质量快速预览
manim -pql pdf_cdf_animation.py SimplePDFCDFAnimation

# 高质量渲染
manim -pqh pdf_cdf_animation.py SimplePDFCDFAnimation

# 4K高质量
manim -pqk pdf_cdf_animation.py SimplePDFCDFAnimation
```

## 📊 动画特点

### 视觉元素
- ✅ **绿色垂直线**：当前积分位置
- ✅ **蓝色阴影区域**：已积分的区域
- ✅ **红色轨迹**：CDF曲线的形成过程
- ✅ **红色点**：CDF上的当前值
- ✅ **实时数值**：显示当前x值和F(x)值

### 教学价值
1. **直观理解**：PDF积分 = CDF值
2. **单调性**：CDF严格单调递增
3. **归一化**：最终CDF达到1
4. **连续性**：平滑的积分过程

## 🎨 自定义选项

### 修改分布类型
在`pdf_cdf_animation.py`中修改`normal_pdf`函数：

```python
# 正态分布（当前）
def normal_pdf(x, mu=0, sigma=1):
    return (1 / (sigma * np.sqrt(2 * np.pi))) * np.exp(-0.5 * ((x - mu) / sigma) ** 2)

# 指数分布
def exponential_pdf(x, lam=1):
    return lam * np.exp(-lam * x) if x >= 0 else 0

# 均匀分布
def uniform_pdf(x, a=-1, b=1):
    return 1/(b-a) if a <= x <= b else 0
```

### 调整动画速度
修改`run_time`参数：
```python
# 更快的动画
self.play(x_tracker.animate.set_value(3), run_time=5)

# 更慢的动画
self.play(x_tracker.animate.set_value(3), run_time=15)
```

### 修改颜色主题
```python
# 深色主题
self.camera.background_color = BLACK
axis_config={"color": WHITE}

# 自定义颜色
pdf_color = BLUE
cdf_color = RED
integral_color = LIGHT_BLUE
```

## 🔧 故障排除

### 常见问题

1. **字体问题**
```bash
# 如果中文显示有问题，使用英文版本
Text("PDF vs CDF", font_size=32, color=BLACK)
```

2. **渲染慢**
```bash
# 使用低质量快速预览
manim -pql --fps 15 pdf_cdf_animation.py SimplePDFCDFAnimation
```

3. **内存不足**
```bash
# 减少积分点数
dx = 0.05  # 原来是0.01
```

### 输出文件位置
```
media/
├── videos/
│   └── pdf_cdf_animation/
│       └── 480p15/
│           └── SimplePDFCDFAnimation.mp4
└── images/
    └── pdf_cdf_animation/
```

## 📚 扩展学习

### 相关概念
- 概率密度函数的性质
- 累积分布函数的性质
- 数值积分方法
- 概率论基础

### 进阶动画
可以基于这个模板创建：
- 多个分布的对比动画
- 离散分布的PMF-CDF关系
- 条件概率的可视化
- 贝叶斯定理的动画演示

## 🎯 使用建议

1. **教学用途**：适合概率论课程演示
2. **学习工具**：帮助理解PDF和CDF关系
3. **研究展示**：学术报告中的可视化工具
4. **自学材料**：配合教材使用效果更佳

## 📝 技术细节

- **框架**：Manim Community v0.17+
- **Python版本**：3.8+
- **依赖库**：numpy, matplotlib
- **渲染引擎**：Cairo/OpenGL
- **输出格式**：MP4视频

希望这个动画能帮助你更好地理解概率密度函数和累积分布函数的关系！
