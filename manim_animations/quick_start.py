#!/usr/bin/env python3
"""
快速启动PDF-CDF动画
直接运行此文件即可
"""

import os
import sys

def main():
    print("🎬 PDF-CDF动画快速启动")
    print("=" * 30)
    
    # 检查当前目录
    current_files = os.listdir('.')
    
    if 'test_animation.py' in current_files:
        print("✅ 找到测试动画文件")
        
        # 直接运行manim命令
        cmd = "manim -pql --fps 15 test_animation.py TestPDFCDF"
        print(f"🚀 执行: {cmd}")
        
        os.system(cmd)
        
    elif 'pdf_cdf_animation.py' in current_files:
        print("✅ 找到完整动画文件")
        
        cmd = "manim -pql pdf_cdf_animation.py SimplePDFCDFAnimation"
        print(f"🚀 执行: {cmd}")
        
        os.system(cmd)
        
    else:
        print("❌ 未找到动画文件")
        print("请确保在正确的目录中运行此脚本")

if __name__ == "__main__":
    main()
